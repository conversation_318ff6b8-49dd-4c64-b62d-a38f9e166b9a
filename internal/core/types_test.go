package core_test

import (
	"testing"

	"github.com/stretchr/testify/require"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func TestRepositoryFromURL(t *testing.T) {
	require := require.New(t)

	tests := []struct {
		name     string
		input    string
		expected core.Repository
	}{
		{
			name:  "simple",
			input: "https://github.example.com/org/repo",
			expected: core.Repository{
				URL:   "https://github.example.com/org/repo",
				Owner: "org",
				Name:  "repo",
			},
		},
		{
			name:  "simple - .git suffix",
			input: "https://github.example.com/org/repo.git",
			expected: core.Repository{
				URL:   "https://github.example.com/org/repo",
				Owner: "org",
				Name:  "repo",
			},
		},
		{
			name:  "with module path",
			input: "https://github.example.com/org/repo//some/subdirectory",
			expected: core.Repository{
				URL:   "https://github.example.com/org/repo",
				Owner: "org",
				Name:  "repo",
			},
		},
		{
			name:  "with module path and .git suffix",
			input: "https://github.example.com/org/repo.git//some/subdirectory",
			expected: core.Repository{
				URL:   "https://github.example.com/org/repo",
				Owner: "org",
				Name:  "repo",
			},
		},
		{
			name:  "with reference",
			input: "https://github.example.com/org/repo?ref=abc",
			expected: core.Repository{
				URL:   "https://github.example.com/org/repo",
				Owner: "org",
				Name:  "repo",
			},
		},
		{
			name:  "with reference and module path",
			input: "https://github.example.com/org/repo//some/subdirectory?ref=abc",
			expected: core.Repository{
				URL:   "https://github.example.com/org/repo",
				Owner: "org",
				Name:  "repo",
			},
		},
		{
			name:  "with reference and module path and .git suffix",
			input: "https://github.example.com/org/repo.git//some/subdirectory?ref=abc",
			expected: core.Repository{
				URL:   "https://github.example.com/org/repo",
				Owner: "org",
				Name:  "repo",
			},
		},
	}

	for idx, test := range tests {
		r := core.RepositoryFromURL(test.input)
		require.Equalf(test.expected.Owner, r.Owner, "[%d] %s - repo owner", idx, test.name)
		require.Equalf(test.expected.Name, r.Name, "[%d] %s - repo name", idx, test.name)
		require.Equalf(test.expected.URL, r.URL, "[%d] %s - repo url", idx, test.name)
	}
}

func TestVersionFromString(t *testing.T) {
	require := require.New(t)

	repo := core.Repository{}

	tests := []struct {
		name     string
		input    string
		expected core.Version
	}{
		{
			name:  "no prefix",
			input: "v1.2.3",
			expected: core.Version{
				FullVersion: "v1.2.3",
				Comparable:  "v1.2.3",
				Prefix:      "",
				PreRelease:  false,
			},
		},
		{
			name:  "prefixed",
			input: "scope/v1.2.3",
			expected: core.Version{
				FullVersion: "scope/v1.2.3",
				Comparable:  "v1.2.3",
				Prefix:      "scope",
				PreRelease:  false,
			},
		},
		{
			name:  "no prefix, release candidate",
			input: "v1.2.3-rc1",
			expected: core.Version{
				FullVersion: "v1.2.3-rc1",
				Comparable:  "v1.2.3-rc1",
				Prefix:      "",
				PreRelease:  true,
			},
		},
		{
			name:  "prefix, release candidate",
			input: "scope/v1.2.3-rc1",
			expected: core.Version{
				FullVersion: "scope/v1.2.3-rc1",
				Comparable:  "v1.2.3-rc1",
				Prefix:      "scope",
				PreRelease:  true,
			},
		},
		{
			name:  "no semver, just a word",
			input: "my-release",
			expected: core.Version{
				FullVersion: "my-release",
				Comparable:  "my-release",
				Prefix:      "",
				PreRelease:  false,
			},
		},
	}

	for testIdx, test := range tests {
		v := core.VersionFromString(repo, test.input)
		require.Equalf(test.expected.FullVersion, v.FullVersion, "[%d] %s: full version", testIdx, test.name)
		require.Equalf(test.expected.Comparable, v.Comparable, "[%d] %s: comparable", testIdx, test.name)
		require.Equalf(test.expected.Prefix, v.Prefix, "[%d] %s: prefix", testIdx, test.name)
		require.Equalf(test.expected.PreRelease, v.PreRelease, "[%d] %s: is pre release", testIdx, test.name)
	}
}
