package core

import (
	"fmt"
	"net/http"
	"strings"

	"log/slog"

	"github.com/blang/semver"
)

// Instance represents an instance/deployment of a module in a specific enviroment
type Instance struct {
	Qualifiers map[string]string // all information to identify the instance (account, clustet name, region, ...)
	Parameters map[string]string // additional information
	Version    Version
}

// EqualIdentity tests if another instances represent the same instance based on their qualifiers
func (i *Instance) EqualIdentity(other *Instance) bool {
	if len(i.Qualifiers) != len(other.Qualifiers) {
		return false
	}

	for key, value := range i.Qualifiers {
		otherValue, found := other.Qualifiers[key]
		if !found {
			return false
		}
		if value != otherValue {
			return false
		}
	}

	return true
}

func (i *Instance) SlogAttributes() []slog.Attr {
	attr := []slog.Attr{}

	attr = append(attr, i.Version.SlogAttributes()...)

	for key, value := range i.Qualifiers {
		attr = append(attr, slog.String(
			fmt.Sprintf("qualifier-%s", key),
			value,
		))
	}

	return attr
}

// Repository represents a GitHub repository definition
type Repository struct {
	URL   string
	Host  string
	Owner string
	Name  string
}

// RepositoryFromURL parses a given URL to a repository struct
func RepositoryFromURL(url string) Repository {
	splitRef := strings.Split(url, "?")
	splits := strings.Split(strings.TrimPrefix(splitRef[0], "https://"), "/")
	r := Repository{}

	r.Host = splits[0]
	r.Owner = splits[1]
	r.Name = strings.TrimSuffix(splits[2], ".git")
	r.URL = fmt.Sprintf("https://%s/%s/%s", splits[0], r.Owner, r.Name)

	return r
}

// Equal returns if the repository is equal to another repository
func (r *Repository) Equal(other *Repository) bool {
	return r.URL == other.URL
}

func (r *Repository) SlogAttributes() []slog.Attr {
	attr := []slog.Attr{
		slog.String("owner", r.Owner),
		slog.String("name", r.Name),
		slog.String("host", r.Host),
	}

	return attr
}

// Version represents available versions of a module
type Version struct {
	Repository  Repository
	Comparable  string // only the comparable (semver) part
	Prefix      string // only the prefix / compatibility part
	FullVersion string // the full version to be referenced
	PreRelease  bool   // is version is a prerelease
}

// EqualIdentity tests if another version represent the same version based on their repositories and prefixes
func (v *Version) EqualIdentity(other *Version) bool {
	if !v.Repository.Equal(&other.Repository) {
		return false
	}
	return v.Prefix == other.Prefix
}

// VersionFromString parses a raw version string to a struct
func VersionFromString(repo Repository, raw string) Version {
	v := Version{}
	v.FullVersion = raw

	v.Repository = repo

	parts := strings.Split(raw, "/")
	if len(parts) == 1 {
		v.Comparable = raw
	} else if len(parts) == 2 {
		v.Prefix = parts[0]
		v.Comparable = parts[1]
	} else if len(parts) > 2 {
		v.Prefix = strings.Join(parts[0:len(parts)-1], "/")
		v.Comparable = parts[len(parts)-1]
	} else {
		panic("invalid length of tagt split")
	}

	sver, isSemver := v.Semver()
	v.PreRelease = !(!isSemver || len(sver.Pre)+len(sver.Build) == 0)

	return v
}

// Semver returns a semver struct of Comparable and if complies to Semver syntax
func (v *Version) Semver() (semver.Version, bool) {
	semver, err := semver.Make(strings.TrimPrefix(v.Comparable, "v"))
	return semver, err == nil
}

func (v *Version) SlogAttributes() []slog.Attr {
	attr := []slog.Attr{
		slog.String("fullVersion", v.FullVersion),
	}

	attr = append(attr, v.Repository.SlogAttributes()...)

	return attr
}

// DataServiceInjector provides functionality to all service implementation
type DataServiceInjector interface {
	StoreInstance(Instance) error
	StoreVersion(Version) error
	GetInstances(InstanceSearchOptions) ([]Instance, error)
	GetVersions() ([]Version, error)
	DeleteInstance(Instance) error
	DeleteVersion(Version) error
}

// Service is an abstraction for all services to be loaded by the application
type Service interface {
	Start() error
	Stop() error
	InjectLogger(*slog.Logger)
	ID() string
}
type InstanceHandler func(Instance) error

type DataService interface {
	Service
	Inject(DataServiceInjector)
	InstanceHandlers() []InstanceHandler
}

type Runtime interface {
	RegisterService(Service) error
	Start() error
	Stop() error
}

type StorageService interface {
	Service
	StoreInstance(Instance) error
	StoreVersion(Version) error
	GetInstances(InstanceSearchOptions) ([]Instance, error)
	GetVersions() ([]Version, error)
	DeleteInstance(Instance) error
	DeleteVersion(Version) error
}

type WebServiceInjector interface {
	GetInstances(InstanceSearchOptions) ([]Instance, error)
	GetVersions() ([]Version, error)
}

type PresentationService interface {
	Service
	Inject(WebServiceInjector)
	HttpPathPrefix() string
	Mux() (*http.ServeMux, error)
}

// InstanceSearchOptions provides the capability to filter instances
// while getting them.
//
// Default and empty options are ignored, so no filtering is applied.
type InstanceSearchOptions struct {
	Kind          []string // Must match one of the elements exactly
	QualifierText []string // Searches over all qualifiers, e.g. 'eu' can be in the region, environment, name, ...
	ClusterName   []string // Must match one of the cluster names exactly
}
