
document.addEventListener('DOMContentLoaded', function() {

    class Instance {
        constructor(instance, versions) {
            this.instance = instance
            this.latestVersion = Instance.getLatestVersion(this.instance, versions)

            if (this.latestVersion) {
                let semverCurrent = new SemVer(this.instance.Version.Comparable)
                let semverLatest = new SemVer(this.latestVersion.Comparable)

                this.update = new Update(semverCurrent, semverLatest)
                this.updateType = this.update.getType()
            } else {
                this.updateType = ''
            }
        }

        createHtmlElement() {
            // Extract key information
            const environment = this.instance.Qualifiers.environment || 'Unknown'
            const kind = this.instance.Qualifiers.kind || 'Unknown'
            const name = this.instance.Qualifiers.name || 'Unknown'
            const parent = this.instance.Qualifiers.parent || ''
            const repoPath = this.instance.Qualifiers.repoPath || ''
            // Determine the best cluster/environment name to display
            const clusterName = this.instance.Qualifiers.clusterName ||
                               this.instance.Qualifiers.environment ||
                               this.instance.Qualifiers.region ||
                               'Unknown'

            // Build status badges and parameters
            let statusBadges = ''
            let actionButtons = ''
            let additionalParams = []

            // Process parameters for badges and buttons
            for (const pKey in this.instance.Parameters) {
                if (pKey.startsWith("_")) {
                    continue
                } else if (this.instance.Parameters[pKey].startsWith('https://')) {
                    // Create action buttons for URLs
                    actionButtons += `
                        <a class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-vf-gray hover:bg-vf-dark-gray rounded-md transition-colors duration-200"
                           href="${this.instance.Parameters[pKey]}" target="_blank">
                            ${pKey}
                        </a>`
                } else if (this.instance.Parameters[pKey].startsWith('badge:')) {
                    // Handle inline badge data format: badge:status:color
                    const badgeData = this.instance.Parameters[pKey].substring(6).split(':')
                    if (badgeData.length === 2) {
                        const [status, color] = badgeData
                        statusBadges += this.createInlineBadge(status, color)
                    }
                } else {
                    // Add to additional parameters
                    additionalParams.push(`<div class="text-xs text-gray-600"><span class="font-medium text-gray-800">${pKey}:</span> ${this.instance.Parameters[pKey]}</div>`)
                }
            }

            // Determine update status and card styling
            let updateBadge = ''
            let cardBorderClass = 'border border-gray-200'
            let updateType = 'None'

            if (this.latestVersion && this.update) {
                updateType = this.update.getType()

                switch (updateType) {
                    case 'Major':
                        cardBorderClass = 'border-l-4 border-l-vf-orange border-t border-r border-b border-gray-200'
                        updateBadge = `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Major Update</span>`
                        break
                    case 'Minor':
                        cardBorderClass = 'border-l-4 border-l-vf-lemon-yellow border-t border-r border-b border-gray-200'
                        updateBadge = `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Minor Update</span>`
                        break
                    case 'Patch':
                        cardBorderClass = 'border-l-4 border-l-vf-aqua-blue border-t border-r border-b border-gray-200'
                        updateBadge = `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Patch Update</span>`
                        break
                    case 'None':
                        cardBorderClass = 'border-l-4 border-l-vf-spring-green border-t border-r border-b border-gray-200'
                        updateBadge = `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Up to date</span>`
                        break
                }
            }

            return `
                <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 ${cardBorderClass} overflow-hidden">
                    <!-- Header with app name and status badges -->
                    <div class="px-4 py-3 border-b border-gray-100">
                        <div class="flex flex-col gap-3">
                            <!-- App name - always fully visible -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 break-words leading-tight" title="${name}">
                                    ${name}
                                </h3>
                            </div>

                            <!-- Secondary info and badges -->
                            <div class="flex items-center justify-between gap-2">
                                <div class="flex items-center gap-2 min-w-0">
                                    <span class="text-sm text-gray-600 capitalize flex-shrink-0">${kind}</span>
                                </div>
                                <div class="flex items-center space-x-2 flex-shrink-0">
                                    ${statusBadges}
                                    ${updateBadge}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cluster/Environment name - prominently displayed -->
                    <div class="px-4 py-2 bg-gray-50 border-b border-gray-200">
                        <div class="text-center">
                            <span class="text-sm font-medium text-gray-700 break-words" title="${clusterName}">
                                ${clusterName}
                            </span>
                        </div>
                    </div>

                    <!-- Content sections -->
                    <div class="p-4 space-y-4">
                        <!-- Version information -->
                        <div class="bg-gray-50 rounded-lg p-3">
                            <div class="space-y-2">
                                <div>
                                    <div class="text-sm font-medium text-gray-700 mb-1">Current Version</div>
                                    <div class="text-sm font-mono text-gray-900 break-all" title="${this.instance.Version.FullVersion}">
                                        ${this.instance.Version.FullVersion}
                                    </div>
                                </div>
                                <div>
                                    <div class="text-xs text-gray-600 mb-1">Latest Available</div>
                                    <div class="text-xs font-mono font-medium text-gray-800 break-all" title="${this.latestVersion ? this.latestVersion.FullVersion : 'Not found'}">
                                        ${this.latestVersion ? this.latestVersion.FullVersion : 'Not found'}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Metadata -->
                        <div class="space-y-3">
                            ${parent ? `
                                <div class="text-sm">
                                    <div class="text-gray-600 mb-1">Parent</div>
                                    <div class="text-gray-900 font-medium break-words" title="${parent}">${parent}</div>
                                </div>
                            ` : ''}
                            ${repoPath ? `
                                <div class="text-sm">
                                    <div class="text-gray-600 mb-1">Repository</div>
                                    <div class="text-gray-900 break-all font-mono text-xs" title="${repoPath}">${repoPath}</div>
                                </div>
                            ` : ''}
                            ${additionalParams.length > 0 ? `
                                <div class="pt-2 border-t border-gray-100 space-y-2">
                                    ${additionalParams.join('')}
                                </div>
                            ` : ''}
                        </div>

                        <!-- Action buttons -->
                        ${actionButtons ? `
                            <div class="flex flex-wrap gap-2 pt-3 border-t border-gray-100">
                                ${actionButtons}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `
        }

        getUpdateType() {
            return this.updateType
        }

        // createInlineBadge generates a CSS-based badge for better browser compatibility
        createInlineBadge(status, color) {
            return `<div class="inline-flex items-center rounded-md overflow-hidden text-xs font-medium shadow-sm">
                <div class="bg-gray-600 text-white px-2 py-1">ArgoCD</div>
                <div style="background-color: ${color};" class="text-white px-2 py-1">${status}</div>
            </div>`
        }

        static getLatestVersion(instance, versions) {
            if (instance.latestVersion) {
                return instance.latestVersion
            }

            // Find all matching versions for the same repository and prefix
            const matchingVersions = versions.filter(v =>
                v.Repository.URL === instance.Version.Repository.URL &&
                v.Prefix === instance.Version.Prefix
            )

            if (matchingVersions.length === 0) {
                return null
            }

            // Sort versions by semver to find the latest
            const sortedVersions = matchingVersions.sort((a, b) => {
                const semverA = new SemVer(a.Comparable)
                const semverB = new SemVer(b.Comparable)

                if (!semverA.major || !semverB.major) {
                    // Fallback to string comparison if semver parsing fails
                    return b.Comparable.localeCompare(a.Comparable)
                }

                if (semverA.major !== semverB.major) return semverB.major - semverA.major
                if (semverA.minor !== semverB.minor) return semverB.minor - semverA.minor
                return semverB.patch - semverA.patch
            })

            instance.latestVersion = sortedVersions[0]
            return sortedVersions[0]
        }
    }

    class SemVer {

        static semverRegex = /^v?(\d+)\.(\d+)\.(\d+)(?:-(.+))?$/

        constructor(version) {
            const match = version.match(SemVer.semverRegex)

            if (!match) {
                return
            }

            this.version = match[0]
            this.major = parseInt(match[1], 10)
            this.minor = parseInt(match[2], 10)
            this.patch = parseInt(match[3], 10)
        }
    }

    class Update {

        static TypeMajor = "Major"
        static TypeMinor = "Minor"
        static TypePatch = "Patch"
        static TypeNone = "None"

        constructor(one, two) {
            if (!one.major || !two.major) {
                this.type = Update.TypeNone
            }

            if (one.major !== two.major) {
                this.type = Update.TypeMajor
            } else if (one.minor !== two.minor) {
                this.type = Update.TypeMinor
            } else if (one.patch !== two.patch) {
                this.type = Update.TypePatch
            } else {
                this.type = Update.TypeNone
            }
        }

        getType() {
            return this.type
        }

        tailwindBorderClasses() {
            let classes = "border-l-8"
            switch (this.type) {
                case Update.TypePatch:
                    classes += " border-l-vf-aqua-blue"
                    break;
                case Update.TypeMinor:
                    classes += " border-l-vf-lemon-yellow"
                    break;
                case Update.TypeMajor:
                    classes += " border-l-vf-orange"
                    break;
                case Update.TypeNone:
                    classes += " border-l-vf-spring-green"
                    break;
            }
            return classes
        }
    }

    class UI {
        constructor() {
            this.contentElement = document.getElementById("content")
            this.searchForm = document.getElementById("searchForm")
            this.selectedClusters = new Set()
            this.availableClusters = new Set()

            if (instances.length === 0) {
                this.setContent(`
                <div class="w-full flex flex-col items-center text-vf-gray italic">
                    <div>No instances found for the current search.</div>
                    <div class="mt-2">
                        Use the text box to search for any property like AWS region, account short name or application name.
                        Use commas to search for multiple terms simultaneously while an instance has to match all of them.
                        Use double quotes to request exact matches (e.g. <span class="italic">"datadog"</span> does not match <span class="italic">datadog-psql</span> )
                        Use the checkboxes to limit your search to one kind only, if needed.
                    </div>
                    <div class="mt-2">
                        Please note: To not overload the dashboard, <span class="font-semibold">you cannot get all instances at once</span>. You must use at least
                        one search option to get results.
                    </div>
                </div>
                `)
                return
            }

            // Initialize cluster dropdown
            this.initializeClusterDropdown()

            document.querySelectorAll('#updateTypes input[type=checkbox]').forEach(box => {
                box.addEventListener('change', () => {
                    this.render()
                })
            })

            this.render()
        }

        setContent(content) {
            this.contentElement.innerHTML = content
        }

        initializeClusterDropdown() {
            const dropdownButton = document.getElementById('clusterDropdownButton')
            const dropdownMenu = document.getElementById('clusterDropdownMenu')
            const searchInput = document.getElementById('clusterSearchInput')

            if (!dropdownButton || !dropdownMenu) return

            // Parse initial selected clusters from URL
            const urlParams = new URLSearchParams(window.location.search)
            const initialClusters = urlParams.getAll('clusterName')
            initialClusters.forEach(cluster => this.selectedClusters.add(cluster))

            // Toggle dropdown
            dropdownButton.addEventListener('click', (e) => {
                e.preventDefault()
                dropdownMenu.classList.toggle('hidden')
                if (!dropdownMenu.classList.contains('hidden')) {
                    this.updateAvailableClusters()
                    this.renderClusterOptions()
                    searchInput.focus()
                }
            })

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!dropdownButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                    dropdownMenu.classList.add('hidden')
                }
            })

            // Search functionality
            searchInput.addEventListener('input', () => {
                this.renderClusterOptions()
            })

            this.updateDropdownText()
            this.updateHiddenInputs()
        }

        updateAvailableClusters() {
            this.availableClusters.clear()

            // Get currently filtered instances (excluding cluster filter)
            const filteredInstances = this.getFilteredInstances(false) // false = don't apply cluster filter

            filteredInstances.forEach(instance => {
                const clusterName = instance.instance.Qualifiers.clusterName ||
                                   instance.instance.Qualifiers.environment ||
                                   instance.instance.Qualifiers.region ||
                                   ''
                if (clusterName) {
                    this.availableClusters.add(clusterName)
                }
            })
        }

        renderClusterOptions() {
            const optionsContainer = document.getElementById('clusterOptions')
            const searchInput = document.getElementById('clusterSearchInput')

            if (!optionsContainer) return

            const searchTerm = searchInput.value.toLowerCase()
            const sortedClusters = Array.from(this.availableClusters).sort()

            optionsContainer.innerHTML = ''

            sortedClusters
                .filter(cluster => cluster.toLowerCase().includes(searchTerm))
                .forEach(cluster => {
                    const isSelected = this.selectedClusters.has(cluster)
                    const option = document.createElement('div')
                    option.className = `px-3 py-2 cursor-pointer hover:bg-gray-100 flex items-center ${isSelected ? 'bg-blue-50' : ''}`
                    option.innerHTML = `
                        <input type="checkbox" ${isSelected ? 'checked' : ''} class="mr-2 accent-vf-primary">
                        <span class="flex-1">${cluster}</span>
                    `

                    option.addEventListener('click', (e) => {
                        e.preventDefault()
                        if (this.selectedClusters.has(cluster)) {
                            this.selectedClusters.delete(cluster)
                        } else {
                            this.selectedClusters.add(cluster)
                        }
                        this.updateDropdownText()
                        this.updateHiddenInputs()
                        this.renderClusterOptions()
                        this.render()
                    })

                    optionsContainer.appendChild(option)
                })
        }

        updateDropdownText() {
            const dropdownText = document.getElementById('clusterDropdownText')
            if (!dropdownText) return

            if (this.selectedClusters.size === 0) {
                dropdownText.textContent = 'Select clusters...'
                dropdownText.className = 'block truncate text-gray-500'
            } else if (this.selectedClusters.size === 1) {
                dropdownText.textContent = Array.from(this.selectedClusters)[0]
                dropdownText.className = 'block truncate'
            } else {
                dropdownText.textContent = `${this.selectedClusters.size} clusters selected`
                dropdownText.className = 'block truncate'
            }
        }

        updateHiddenInputs() {
            const container = document.getElementById('clusterHiddenInputs')
            if (!container) return

            container.innerHTML = ''
            this.selectedClusters.forEach(cluster => {
                const input = document.createElement('input')
                input.type = 'hidden'
                input.name = 'clusterName'
                input.value = cluster
                container.appendChild(input)
            })
        }

        getFilteredInstances(applyClusterFilter = true) {
            let filterUpdateTypes = []
            let formData = new FormData(this.searchForm)

            for (const touple of formData.entries()) {
                if (touple[0] === "updateType") {
                    filterUpdateTypes.push(touple[1])
                }
            }

            return instances
                .map(i => new Instance(i, versions))
                .filter(i => {
                    // Filter by update type
                    const updateTypeMatch = filterUpdateTypes.length === 0 || filterUpdateTypes.indexOf(i.getUpdateType()) !== -1;

                    // Filter by cluster name (only if requested)
                    let clusterNameMatch = true;
                    if (applyClusterFilter && this.selectedClusters.size > 0) {
                        const instanceClusterName = i.instance.Qualifiers.clusterName ||
                                                   i.instance.Qualifiers.environment ||
                                                   i.instance.Qualifiers.region ||
                                                   '';
                        clusterNameMatch = this.selectedClusters.has(instanceClusterName);
                    }

                    return updateTypeMatch && clusterNameMatch;
                })
        }

        async render() {
            // Update URL parameters
            const urlParams = new URLSearchParams(window.location.search)
            urlParams.delete("updateType")
            urlParams.delete("clusterName")

            let formData = new FormData(this.searchForm)
            for (const touple of formData.entries()) {
                if (touple[0] === "updateType") {
                    urlParams.append('updateType', touple[1]);
                }
            }

            // Add selected clusters to URL
            this.selectedClusters.forEach(cluster => {
                urlParams.append('clusterName', cluster);
            })

            const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
            history.pushState({}, '', newUrl);

            // Update available clusters based on current filters
            this.updateAvailableClusters()

            // Get filtered instances
            const filteredInstances = this.getFilteredInstances(true)
            const instanceElements = filteredInstances.map((i) => i.createHtmlElement(i, versions))

            if (!instanceElements || instanceElements.length === 0) {
                this.setContent('')
                return
            }
            this.setContent(`
                <h1 class="text-4xl font-bold pb-2">Instances</h1>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 pb-12">
                    ${instanceElements.reduce((a,b) => a+b)}
                </div>`)
        }
    }

    // Initialize the UI
    const ui = new UI()
})
