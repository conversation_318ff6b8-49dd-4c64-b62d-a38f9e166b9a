package webui

import (
	"net/http"

	"log/slog"

	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

/*
	TODO
	make two modes:
	  - for local dev of the frontend, still read file per request
	  - for prod (-> compile flags?), embed files and use template cache
*/

type service struct {
	logger             *slog.Logger
	deferFuncs         []func()
	webServiceInjector core.WebServiceInjector
}

func NewService() (core.Service, error) {
	s := &service{}
	return s, nil
}

func (s *service) Start() error {
	return nil
}

func (s *service) Stop() error {
	for _, fn := range s.deferFuncs {
		fn()
	}
	return nil
}

func (s *service) Inject(injector core.WebServiceInjector) {
	s.webServiceInjector = injector
}

func (s *service) InjectLogger(logger *slog.Logger) {
	s.logger = logger
}

func (s *service) ID() string {
	return "webui"
}

func (s *service) HttpPathPrefix() string {
	return "/"
}

func (s *service) Mux() (*http.ServeMux, error) {
	mux := http.NewServeMux()

	mux.Handle("/static/", s.HandleStatic())
	mux.HandleFunc("/", s.HandleDashboard)

	return mux, nil
}
