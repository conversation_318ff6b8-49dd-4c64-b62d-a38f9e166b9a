<!DOCTYPE html>
<html>
<head>
    <title>CET Cockpit</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <script src="/static/argo-watch.js"></script>
    <script src="/static/tailwindcss-3.3.3.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        "vf-primary": "#e60000",
                        "vf-secondary": "#820000",
                        "vf-gray": "#54575a",
                        "vf-dark-gray": "#25282b",
                        "vf-aqua-blue": "#00b0ca",
                        "vf-spring-green": "#a8b400",
                        "vf-lemon-yellow": "#fecb00",
                        "vf-orange": "#eb9500",
                    }
                }
            }
        }
    </script>
    <style>
        /* Compact checkbox styles */
        .checkbox-mini {
            width: 12px;
            height: 12px;
            border: 1px solid #d1d5db;
            border-radius: 3px;
            position: relative;
            transition: all 0.2s;
        }

        input[type="checkbox"]:checked + .checkbox-mini,
        input[type="checkbox"]:checked + * .checkbox-mini {
            background-color: #e60000;
            border-color: #e60000;
        }

        input[type="checkbox"]:checked + .checkbox-mini::after,
        input[type="checkbox"]:checked + * .checkbox-mini::after {
            content: '';
            position: absolute;
            left: 3px;
            top: 0px;
            width: 3px;
            height: 6px;
            border: solid white;
            border-width: 0 1px 1px 0;
            transform: rotate(45deg);
        }

        /* Compact update filter styles */
        .update-filter-mini {
            display: inline-block;
            padding: 4px 8px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .update-filter-mini:hover {
            border-color: #d1d5db;
            background-color: #f9fafb;
        }

        .update-filter-mini input[type="checkbox"]:checked + .update-filter-mini-content {
            font-weight: 500;
            color: #1f2937;
        }

        .update-filter-mini-content {
            display: flex;
            align-items: center;
            color: #6b7280;
            transition: all 0.2s;
        }

        /* Loading state for search button */
        #searchButton:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) {
            .lg\\:flex-row {
                flex-direction: column;
            }

            #clusterFilter {
                margin-left: 0 !important;
                margin-top: 8px;
            }
        }

        /* Cluster dropdown improvements */
        #clusterDropdownMenu {
            backdrop-filter: blur(8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        #clusterOptions {
            scrollbar-width: thin;
            scrollbar-color: #d1d5db #f9fafb;
        }

        #clusterOptions::-webkit-scrollbar {
            width: 6px;
        }

        #clusterOptions::-webkit-scrollbar-track {
            background: #f9fafb;
            border-radius: 3px;
        }

        #clusterOptions::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
        }

        #clusterOptions::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        /* Ensure dropdown is above other elements */
        .dropdown-container {
            position: relative;
            z-index: 50;
        }

        /* Loading states */
        .animate-pulse-slow {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    </style>
</head>
<body class="bg-neutral-50 flex flex-col min-h-screen">
    <header class="sticky top-0 z-50">
        <nav class="w-full bg-gradient-to-r from-vf-secondary to-vf-primary mb-2 flex justify-center shadow-md">
            <div class="container flex flex-row justify-between py-2">
                <div class="text-white font-bold text-3xl">CET Cockpit</div>
                <a class="flex flex-row items-center hover:bg-vf-secondary rounded-xl px-4" href="https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/" target="_blank">
                    <img src="/static/fa-icon-github.svg" class="w-6 h-6 invert mr-2" />
                    <p class="text-white" >GitHub</p>
                </a>
            </div>
        </nav>
    </header>
    <main class="flex-grow">
        <div class="w-full px-4 sm:px-6 lg:px-8">
            <div class="container mx-auto">
                <form id="searchForm" class="mb-6">
                    <!-- Compact Search Header -->
                    <div class="text-center mb-4">
                        <h1 class="text-2xl font-bold text-gray-900">Search Applications</h1>
                    </div>

                    <!-- Compact Search Card -->
                    <div class="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
                        <!-- Search Input and Filters in one row -->
                        <div class="p-4">
                            <div class="flex flex-col lg:flex-row gap-4 items-start">
                                <!-- Search Input -->
                                <div class="flex-1">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                            </svg>
                                        </div>
                                        <input
                                            type="text"
                                            name="text"
                                            placeholder="Search applications, environments, regions..."
                                            value="{{ .SearchOptions.QualifierText | join ", " }}"
                                            class="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-vf-primary focus:border-vf-primary text-sm"
                                        />
                                    </div>
                                </div>

                                <!-- Application Types -->
                                <div class="flex items-center gap-2">
                                    <span class="text-xs font-medium text-gray-600 whitespace-nowrap">Type:</span>
                                    <label class="inline-flex items-center px-2 py-1 rounded border border-gray-200 hover:bg-gray-50 cursor-pointer text-xs">
                                        <input type="checkbox" name="kind" value="terragrunt" class="sr-only" {{ if has "terragrunt" .SearchOptions.Kind }}checked{{ end }} />
                                        <div class="checkbox-mini mr-1"></div>
                                        <span>Terragrunt</span>
                                    </label>
                                    <label class="inline-flex items-center px-2 py-1 rounded border border-gray-200 hover:bg-gray-50 cursor-pointer text-xs">
                                        <input type="checkbox" name="kind" value="kubernetes" class="sr-only" {{ if has "kubernetes" .SearchOptions.Kind }}checked{{ end }} />
                                        <div class="checkbox-mini mr-1"></div>
                                        <span>Kubernetes</span>
                                    </label>
                                </div>

                                <!-- Search Button -->
                                <button
                                    id="searchButton"
                                    type="submit"
                                    class="px-4 py-2 bg-vf-primary hover:bg-vf-secondary text-white font-medium rounded-md transition-colors flex items-center text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span id="searchButtonText">Search</span>
                                    <svg id="searchSpinner" class="hidden animate-spin ml-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </button>
                            </div>

                            <!-- Update Status Filters -->
                            <div class="mt-3 pt-3 border-t border-gray-100">
                                <div class="flex flex-wrap items-center gap-2">
                                    <span class="text-xs font-medium text-gray-600">Updates:</span>
                                    <div id="updateTypes" class="flex flex-wrap gap-1">
                                        <label class="update-filter-mini">
                                            <input type="checkbox" name="updateType" value="Major" class="sr-only" {{ if has "Major" .UpdateTypes }}checked{{ end }} />
                                            <div class="update-filter-mini-content">
                                                <div class="w-2 h-2 rounded-full bg-vf-orange mr-1"></div>
                                                <span class="text-xs">Major</span>
                                            </div>
                                        </label>
                                        <label class="update-filter-mini">
                                            <input type="checkbox" name="updateType" value="Minor" class="sr-only" {{ if has "Minor" .UpdateTypes }}checked{{ end }} />
                                            <div class="update-filter-mini-content">
                                                <div class="w-2 h-2 rounded-full bg-vf-lemon-yellow mr-1"></div>
                                                <span class="text-xs">Minor</span>
                                            </div>
                                        </label>
                                        <label class="update-filter-mini">
                                            <input type="checkbox" name="updateType" value="Patch" class="sr-only" {{ if has "Patch" .UpdateTypes }}checked{{ end }} />
                                            <div class="update-filter-mini-content">
                                                <div class="w-2 h-2 rounded-full bg-vf-aqua-blue mr-1"></div>
                                                <span class="text-xs">Patch</span>
                                            </div>
                                        </label>
                                        <label class="update-filter-mini">
                                            <input type="checkbox" name="updateType" value="None" class="sr-only" {{ if has "None" .UpdateTypes }}checked{{ end }} />
                                            <div class="update-filter-mini-content">
                                                <div class="w-2 h-2 rounded-full bg-vf-spring-green mr-1"></div>
                                                <span class="text-xs">Up to date</span>
                                            </div>
                                        </label>
                                        <label class="update-filter-mini">
                                            <input type="checkbox" name="updateType" value="" class="sr-only" {{ if has "" .UpdateTypes }}checked{{ end }} />
                                            <div class="update-filter-mini-content">
                                                <div class="w-2 h-2 rounded-full bg-gray-400 mr-1"></div>
                                                <span class="text-xs">Unknown</span>
                                            </div>
                                        </label>
                                    </div>

                                    <!-- Cluster Filter - Only shown when there are search results -->
                                    <div id="clusterFilter" style="display: none;" class="flex items-center gap-2 ml-4">
                                        <span class="text-xs font-medium text-gray-600">Clusters:</span>
                                        <div class="relative dropdown-container">
                                            <button type="button" id="clusterDropdownButton"
                                                    class="px-2 py-1 bg-white border border-gray-300 rounded text-xs hover:border-gray-400 focus:outline-none focus:ring-1 focus:ring-vf-primary">
                                                <span id="clusterDropdownText" class="text-gray-700">Select...</span>
                                                <svg class="inline h-3 w-3 ml-1 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                </svg>
                                            </button>

                                            <div id="clusterDropdownMenu" class="hidden absolute z-50 mt-1 w-64 bg-white shadow-xl max-h-60 rounded-md border border-gray-200 overflow-hidden">
                                                <div class="p-2 border-b border-gray-100">
                                                    <input type="text" id="clusterSearchInput" placeholder="Search clusters..."
                                                           class="w-full px-2 py-1 border border-gray-300 rounded text-xs focus:outline-none focus:ring-1 focus:ring-vf-primary">
                                                </div>
                                                <div id="clusterOptions" class="max-h-48 overflow-y-auto">
                                                    <!-- Options will be populated by JavaScript -->
                                                </div>
                                            </div>

                                            <!-- Hidden inputs for form submission -->
                                            <div id="clusterHiddenInputs"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="mt-4 flex flex-col w-full h-full items-center">
            <div class="container pt-4">
                <!-- Loading spinner for content -->
                <div id="contentLoading" class="hidden flex flex-col items-center justify-center py-12">
                    <svg class="animate-spin h-12 w-12 text-vf-primary mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="text-vf-gray text-lg">Loading instances...</p>
                </div>
                <div id="content"></div>
            </div>
        </div>
    </main>
    <footer>
        <div class="w-full flex flex-row justify-center items-center mt-4 pb-4">
            <p class="text-neutral-400">Managed by Team Tesla</p>
        </div>
    </footer>
    <script>
        var instances=JSON.parse({{ .Instances | default list | mustToJson }})
        var versions=JSON.parse({{ .Versions | default list | mustToJson }})
    </script>
</body>
</html>
