<!DOCTYPE html>
<html>
<head>
    <title>CET Cockpit</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <script src="/static/argo-watch.js"></script>
    <script src="/static/tailwindcss-3.3.3.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        "vf-primary": "#e60000",
                        "vf-secondary": "#820000",
                        "vf-gray": "#54575a",
                        "vf-dark-gray": "#25282b",
                        "vf-aqua-blue": "#00b0ca",
                        "vf-spring-green": "#a8b400",
                        "vf-lemon-yellow": "#fecb00",
                        "vf-orange": "#eb9500",
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-neutral-50 flex flex-col min-h-screen">
    <header class="sticky top-0 z-50">
        <nav class="w-full bg-gradient-to-r from-vf-secondary to-vf-primary mb-2 flex justify-center shadow-md">
            <div class="container flex flex-row justify-between py-2">
                <div class="text-white font-bold text-3xl">CET Cockpit</div>
                <a class="flex flex-row items-center hover:bg-vf-secondary rounded-xl px-4" href="https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/" target="_blank">
                    <img src="/static/fa-icon-github.svg" class="w-6 h-6 invert mr-2" />
                    <p class="text-white" >GitHub</p>
                </a>
            </div>
        </nav>
    </header>
    <main class="flex-grow">
        <div class="flex flex-col w-full h-full items-center">
            <form id="searchForm" class="container pt-4">
                <h1 class="text-4xl font-bold pb-2">Search</h1>
                <div class="flex flex-row justify-center">
                    <input
                        type="text"
                        name="text"
                        placeholder="Search for environment, application name, ..."
                        value="{{ .SearchOptions.QualifierText | join ", " }}"
                        class="w-full pt-3 pb-2 bg-transparent appearance-none border-0 border-b-2 border-vf-primary focus:border-vf-secondary focus:ring-0 focus:outline-none transition"
                        />
                </div>
                <div class="flex flex-row justify-between mt-2">
                    <div class="flex flex-col justify-center">
                        <fieldset>
                            <div>
                                <label class="mr-6">
                                    <input
                                        type="checkbox"
                                        name="kind"
                                        value="terragrunt"
                                        class="mr-2 accent-vf-primary rounded"
                                        {{ if has "terragrunt" .SearchOptions.Kind }}checked{{ end }}
                                        />Terragrunt
                                </label>
                                <label class="mr-6">
                                    <input
                                        type="checkbox"
                                        name="kind"
                                        value="kubernetes"
                                        class="mr-2 accent-vf-primary rounded"
                                        {{ if has "kubernetes" .SearchOptions.Kind }}checked{{ end }}
                                        />Kubernetes
                                </label>
                            </div>
                        </fieldset>
                    </div>
                    <div>
                        <button
                            id="searchButton"
                            type="submit"
                            class="rounded px-4 py-2 bg-vf-gray hover:bg-vf-dark-gray shadow text-white font-bold"
                            >Search</button>
                    </div>
                </div>

                <div id="updateTypes" class="w-full flex flex-row container items-baseline">
                    <h3 class="text-lg font-bold">Update type:</h3>
                    <div class="rounded px-3 ml-3 bg-white border border-neutral-300 border-l-8 border-l-vf-orange">
                        <label>
                            <input
                                type="checkbox"
                                name="updateType"
                                value="Major"
                                {{ if has "Major" .UpdateTypes }}checked{{ end }}
                                class="mr-2 accent-vf-primary rounded"
                                />Major
                        </label>
                    </div>
                    <div class="rounded px-3 ml-3 bg-white border border-neutral-300 border-l-8 border-l-vf-lemon-yellow">
                        <label>
                            <input
                                type="checkbox"
                                name="updateType"
                                value="Minor"
                                {{ if has "Minor" .UpdateTypes }}checked{{ end }}
                                class="mr-2 accent-vf-primary rounded"
                                />Minor
                        </label>
                    </div>
                    <div class="rounded px-3 ml-3 bg-white border border-neutral-300 border-l-8 border-l-vf-aqua-blue">
                        <label>
                            <input
                                type="checkbox"
                                name="updateType"
                                value="Patch"
                                {{ if has "Patch" .UpdateTypes }}checked{{ end }}
                                class="mr-2 accent-vf-primary rounded"
                                />Patch
                        </label>
                    </div>
                    <div class="rounded px-3 ml-3 bg-white border border-neutral-300 border-l-8 border-l-vf-spring-green">
                        <label>
                            <input
                                type="checkbox"
                                name="updateType"
                                value="None"
                                {{ if has "None" .UpdateTypes }}checked{{ end }}
                                class="mr-2 accent-vf-primary rounded"
                                />None
                        </label>
                    </div>
                    <div class="rounded px-3 ml-3 bg-white border border-neutral-300">
                        <label>
                            <input
                                type="checkbox"
                                name="updateType"
                                value=""
                                {{ if has "" .UpdateTypes }}checked{{ end }}
                                class="mr-2 accent-vf-primary rounded"
                                />Unknown
                        </label>
                    </div>
                </div>

                <!-- Cluster Name Filter - Only shown when there are search results -->
                <div id="clusterFilter" class="w-full flex flex-row container items-baseline mt-4" style="display: none;">
                    <h3 class="text-lg font-bold mr-4">Cluster:</h3>
                    <div class="relative flex-1 max-w-md">
                        <div id="clusterDropdown" class="relative">
                            <button type="button" id="clusterDropdownButton"
                                    class="w-full bg-white border border-gray-300 rounded-md px-4 py-2 text-left shadow-sm focus:outline-none focus:ring-2 focus:ring-vf-primary focus:border-vf-primary">
                                <span id="clusterDropdownText" class="block truncate">Select clusters...</span>
                                <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </span>
                            </button>

                            <div id="clusterDropdownMenu" class="hidden absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
                                <div class="px-3 py-2 border-b border-gray-200">
                                    <input type="text" id="clusterSearchInput" placeholder="Search clusters..."
                                           class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-vf-primary">
                                </div>
                                <div id="clusterOptions" class="py-1">
                                    <!-- Options will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>

                        <!-- Hidden inputs for form submission -->
                        <div id="clusterHiddenInputs"></div>
                    </div>
                </div>
            </form>
        </div>

        <div class="mt-4 flex flex-col w-full h-full items-center">
            <div class="container pt-4">
                <div id="content"></div>
            </div>
        </div>
    </main>
    <footer>
        <div class="w-full flex flex-row justify-center items-center mt-4 pb-4">
            <p class="text-neutral-400">Managed by Team Tesla</p>
        </div>
    </footer>
    <script>
        var instances=JSON.parse({{ .Instances | default list | mustToJson }})
        var versions=JSON.parse({{ .Versions | default list | mustToJson }})
    </script>
</body>
</html>
