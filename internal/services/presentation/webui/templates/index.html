<!DOCTYPE html>
<html>
<head>
    <title>CET Cockpit</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <script src="/static/argo-watch.js"></script>
    <script src="/static/tailwindcss-3.3.3.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        "vf-primary": "#e60000",
                        "vf-secondary": "#820000",
                        "vf-gray": "#54575a",
                        "vf-dark-gray": "#25282b",
                        "vf-aqua-blue": "#00b0ca",
                        "vf-spring-green": "#a8b400",
                        "vf-lemon-yellow": "#fecb00",
                        "vf-orange": "#eb9500",
                    }
                }
            }
        }
    </script>
    <style>
        /* Custom checkbox styles */
        .checkbox-custom {
            width: 16px;
            height: 16px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            position: relative;
            transition: all 0.2s;
        }

        input[type="checkbox"]:checked + .flex .checkbox-custom {
            background-color: #e60000;
            border-color: #e60000;
        }

        input[type="checkbox"]:checked + .flex .checkbox-custom::after {
            content: '';
            position: absolute;
            left: 4px;
            top: 1px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        /* Update filter cards */
        .update-filter-card {
            display: block;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
        }

        .update-filter-card:hover {
            border-color: #d1d5db;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .update-filter-card input[type="checkbox"]:checked + .update-filter-content {
            color: #1f2937;
        }

        .update-filter-card input[type="checkbox"]:checked + .update-filter-content .update-indicator {
            transform: scale(1.1);
            box-shadow: 0 0 0 2px rgba(230, 0, 0, 0.2);
        }

        .update-filter-content {
            display: flex;
            align-items: center;
            color: #6b7280;
            transition: color 0.2s;
        }

        .update-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            transition: all 0.2s;
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
            .update-filter-card {
                padding: 10px;
            }

            .update-filter-content {
                font-size: 0.875rem;
            }

            .update-indicator {
                width: 10px;
                height: 10px;
                margin-right: 6px;
            }
        }

        /* Cluster dropdown improvements */
        #clusterDropdownMenu {
            backdrop-filter: blur(8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        #clusterOptions {
            scrollbar-width: thin;
            scrollbar-color: #d1d5db #f9fafb;
        }

        #clusterOptions::-webkit-scrollbar {
            width: 6px;
        }

        #clusterOptions::-webkit-scrollbar-track {
            background: #f9fafb;
            border-radius: 3px;
        }

        #clusterOptions::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
        }

        #clusterOptions::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        /* Ensure dropdown is above other elements */
        .dropdown-container {
            position: relative;
            z-index: 50;
        }

        /* Loading states */
        .animate-pulse-slow {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    </style>
</head>
<body class="bg-neutral-50 flex flex-col min-h-screen">
    <header class="sticky top-0 z-50">
        <nav class="w-full bg-gradient-to-r from-vf-secondary to-vf-primary mb-2 flex justify-center shadow-md">
            <div class="container flex flex-row justify-between py-2">
                <div class="text-white font-bold text-3xl">CET Cockpit</div>
                <a class="flex flex-row items-center hover:bg-vf-secondary rounded-xl px-4" href="https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/" target="_blank">
                    <img src="/static/fa-icon-github.svg" class="w-6 h-6 invert mr-2" />
                    <p class="text-white" >GitHub</p>
                </a>
            </div>
        </nav>
    </header>
    <main class="flex-grow">
        <div class="flex flex-col w-full h-full items-center px-4 sm:px-6 lg:px-8">
            <div class="w-full">
                <form id="searchForm" class="space-y-6">
                    <!-- Header -->
                    <div class="text-center">
                        <h1 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">Search Applications</h1>
                        <p class="text-gray-600 text-sm sm:text-base">Find and filter your applications across environments</p>
                    </div>

                    <!-- Main Search Card -->
                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                        <!-- Search Input Section -->
                        <div class="p-6 border-b border-gray-100">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <input
                                    type="text"
                                    name="text"
                                    placeholder="Search for applications, environments, regions..."
                                    value="{{ .SearchOptions.QualifierText | join ", " }}"
                                    class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-vf-primary focus:border-vf-primary transition-colors text-gray-900 placeholder-gray-500"
                                />
                            </div>
                        </div>

                        <!-- Filters Section -->
                        <div class="p-6 space-y-6">
                            <!-- Application Types -->
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                                    <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    Application Types
                                </h3>
                                <div class="flex flex-wrap gap-3">
                                    <label class="inline-flex items-center px-4 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors">
                                        <input
                                            type="checkbox"
                                            name="kind"
                                            value="terragrunt"
                                            class="sr-only"
                                            {{ if has "terragrunt" .SearchOptions.Kind }}checked{{ end }}
                                        />
                                        <div class="flex items-center">
                                            <div class="checkbox-custom mr-2"></div>
                                            <span class="text-sm font-medium text-gray-700">Terragrunt</span>
                                        </div>
                                    </label>
                                    <label class="inline-flex items-center px-4 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors">
                                        <input
                                            type="checkbox"
                                            name="kind"
                                            value="kubernetes"
                                            class="sr-only"
                                            {{ if has "kubernetes" .SearchOptions.Kind }}checked{{ end }}
                                        />
                                        <div class="flex items-center">
                                            <div class="checkbox-custom mr-2"></div>
                                            <span class="text-sm font-medium text-gray-700">Kubernetes</span>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- Update Status -->
                            <div>
                                <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                                    <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Update Status
                                </h3>
                                <div id="updateTypes" class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3">
                                    <label class="update-filter-card update-major">
                                        <input
                                            type="checkbox"
                                            name="updateType"
                                            value="Major"
                                            class="sr-only"
                                            {{ if has "Major" .UpdateTypes }}checked{{ end }}
                                        />
                                        <div class="update-filter-content">
                                            <div class="update-indicator bg-vf-orange"></div>
                                            <span class="text-sm font-medium">Major</span>
                                        </div>
                                    </label>
                                    <label class="update-filter-card update-minor">
                                        <input
                                            type="checkbox"
                                            name="updateType"
                                            value="Minor"
                                            class="sr-only"
                                            {{ if has "Minor" .UpdateTypes }}checked{{ end }}
                                        />
                                        <div class="update-filter-content">
                                            <div class="update-indicator bg-vf-lemon-yellow"></div>
                                            <span class="text-sm font-medium">Minor</span>
                                        </div>
                                    </label>
                                    <label class="update-filter-card update-patch">
                                        <input
                                            type="checkbox"
                                            name="updateType"
                                            value="Patch"
                                            class="sr-only"
                                            {{ if has "Patch" .UpdateTypes }}checked{{ end }}
                                        />
                                        <div class="update-filter-content">
                                            <div class="update-indicator bg-vf-aqua-blue"></div>
                                            <span class="text-sm font-medium">Patch</span>
                                        </div>
                                    </label>
                                    <label class="update-filter-card update-none">
                                        <input
                                            type="checkbox"
                                            name="updateType"
                                            value="None"
                                            class="sr-only"
                                            {{ if has "None" .UpdateTypes }}checked{{ end }}
                                        />
                                        <div class="update-filter-content">
                                            <div class="update-indicator bg-vf-spring-green"></div>
                                            <span class="text-sm font-medium">Up to date</span>
                                        </div>
                                    </label>
                                    <label class="update-filter-card update-unknown">
                                        <input
                                            type="checkbox"
                                            name="updateType"
                                            value=""
                                            class="sr-only"
                                            {{ if has "" .UpdateTypes }}checked{{ end }}
                                        />
                                        <div class="update-filter-content">
                                            <div class="update-indicator bg-gray-400"></div>
                                            <span class="text-sm font-medium">Unknown</span>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- Cluster Filter - Only shown when there are search results -->
                            <div id="clusterFilter" style="display: none;">
                                <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                                    <svg class="h-4 w-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
                                    </svg>
                                    Clusters
                                </h3>
                                <div class="relative dropdown-container">
                                    <div id="clusterDropdown" class="relative">
                                        <button type="button" id="clusterDropdownButton"
                                                class="w-full bg-white border border-gray-300 rounded-lg px-4 py-3 text-left shadow-sm hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-vf-primary focus:border-vf-primary transition-colors">
                                            <span id="clusterDropdownText" class="block truncate text-gray-700">Select clusters...</span>
                                            <span class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                                <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                </svg>
                                            </span>
                                        </button>

                                        <div id="clusterDropdownMenu" class="hidden absolute z-50 mt-2 w-full bg-white shadow-xl max-h-80 rounded-lg border border-gray-200 overflow-hidden">
                                            <div class="p-3 border-b border-gray-100 bg-white sticky top-0">
                                                <input type="text" id="clusterSearchInput" placeholder="Search clusters..."
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-vf-primary focus:border-vf-primary">
                                            </div>
                                            <div id="clusterOptions" class="max-h-60 overflow-y-auto overscroll-contain">
                                                <!-- Options will be populated by JavaScript -->
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Hidden inputs for form submission -->
                                    <div id="clusterHiddenInputs"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Search Button -->
                        <div class="px-6 py-4 bg-gray-50 border-t border-gray-100">
                            <button
                                id="searchButton"
                                type="submit"
                                class="w-full sm:w-auto px-8 py-3 bg-vf-primary hover:bg-vf-secondary text-white font-semibold rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center disabled:opacity-75 disabled:cursor-not-allowed"
                            >
                                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                <span id="searchButtonText">Search Applications</span>
                                <svg id="searchSpinner" class="hidden animate-spin ml-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="mt-4 flex flex-col w-full h-full items-center">
            <div class="container pt-4">
                <!-- Loading spinner for content -->
                <div id="contentLoading" class="hidden flex flex-col items-center justify-center py-12">
                    <svg class="animate-spin h-12 w-12 text-vf-primary mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="text-vf-gray text-lg">Loading instances...</p>
                </div>
                <div id="content"></div>
            </div>
        </div>
    </main>
    <footer>
        <div class="w-full flex flex-row justify-center items-center mt-4 pb-4">
            <p class="text-neutral-400">Managed by Team Tesla</p>
        </div>
    </footer>
    <script>
        var instances=JSON.parse({{ .Instances | default list | mustToJson }})
        var versions=JSON.parse({{ .Versions | default list | mustToJson }})
    </script>
</body>
</html>
