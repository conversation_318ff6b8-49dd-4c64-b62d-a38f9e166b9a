package webui

import (
	"html/template"
	"net/http"
	"os"
	"strings"

	"github.com/Masterminds/sprig/v3"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func (s *service) HandleStatic() http.Handler {
	fs := http.FileServer(http.Dir("./internal/services/presentation/webui/static"))
	return http.StripPrefix("/static/", fs)
}

type dashboardData struct {
	Instances     []core.Instance
	Versions      []core.Version
	SearchOptions core.InstanceSearchOptions
	UpdateTypes   []string
}

func (s *service) HandleDashboard(w http.ResponseWriter, r *http.Request) {
	queryParams := r.URL.Query()
	queryText := queryParams.Get("text")
	queryKind := queryParams.Get("kind")
	queryClusterName := queryParams.Get("clusterName")

	search := false
	data := dashboardData{
		SearchOptions: core.InstanceSearchOptions{},
		UpdateTypes:   queryParams["updateType"],
	}

	if queryKind != "" {
		search = true
		data.SearchOptions.Kind = strings.Split(queryKind, ",")
	}

	if len(queryText) > 0 { // don't create a list with one empty string
		search = true
		data.SearchOptions.QualifierText = strings.Split(queryText, ",")
	}

	if queryClusterName != "" {
		search = true
		data.SearchOptions.ClusterName = strings.Split(queryClusterName, ",")
	}

	if search {
		versions, err := s.webServiceInjector.GetVersions()
		if err != nil {
			s.logger.Error("cannot get versions for dashboard", "msg", err.Error())
			// this is bad, but the dashboard can still show instances without the lastest versions
		}
		data.Versions = versions

		instances, err := s.webServiceInjector.GetInstances(data.SearchOptions)
		if err != nil {
			s.logger.Error("cannot get instances for dashboard", "msg", err.Error())
			http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
			return
		}
		data.Instances = instances
	}

	templatePath := "./internal/services/presentation/webui/templates/index.html"
	b, err := os.ReadFile(templatePath)
	if err != nil {
		s.logger.Error("cannot open template", "error", err.Error(), "filepath", templatePath)
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	tmpl, err := template.New("index").Funcs(sprig.FuncMap()).Parse(string(b))
	if err != nil {
		s.logger.Error("cannot parse template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	err = tmpl.ExecuteTemplate(w, "index", data)
	if err != nil {
		s.logger.Error("cannot execute template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
	}
}
