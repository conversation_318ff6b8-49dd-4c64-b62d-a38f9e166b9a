package webui

import (
	"html/template"
	"net/http"
	"os"
	"sort"
	"strings"

	"github.com/Masterminds/sprig/v3"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func (s *service) HandleStatic() http.Handler {
	fs := http.FileServer(http.Dir("./internal/services/presentation/webui/static"))
	return http.StripPrefix("/static/", fs)
}

type dashboardData struct {
	Instances     []core.Instance
	Versions      []core.Version
	SearchOptions core.InstanceSearchOptions
	UpdateTypes   []string
	ClusterNames  []string
}

func (s *service) HandleDashboard(w http.ResponseWriter, r *http.Request) {
	queryParams := r.URL.Query()
	queryText := queryParams.Get("text")
	queryKind := queryParams.Get("kind")
	queryClusterName := queryParams.Get("clusterName")

	search := false
	data := dashboardData{
		SearchOptions: core.InstanceSearchOptions{},
		UpdateTypes:   queryParams["updateType"],
	}

	if queryKind != "" {
		search = true
		data.SearchOptions.Kind = strings.Split(queryKind, ",")
	}

	if len(queryText) > 0 { // don't create a list with one empty string
		search = true
		data.SearchOptions.QualifierText = strings.Split(queryText, ",")
	}

	if queryClusterName != "" {
		search = true
		data.SearchOptions.ClusterName = strings.Split(queryClusterName, ",")
	}

	// Always get all instances to extract available cluster names for the filter UI
	allInstances, err := s.webServiceInjector.GetInstances(core.InstanceSearchOptions{})
	if err != nil {
		s.logger.Error("cannot get all instances for cluster names", "msg", err.Error())
		// Continue without cluster names if this fails
	} else {
		// Extract unique cluster names from all instances
		clusterNameSet := make(map[string]bool)
		for _, instance := range allInstances {
			clusterName := ""
			if cn, found := instance.Qualifiers["clusterName"]; found {
				clusterName = cn
			} else if env, found := instance.Qualifiers["environment"]; found {
				clusterName = env
			} else if region, found := instance.Qualifiers["region"]; found {
				clusterName = region
			}
			if clusterName != "" {
				clusterNameSet[clusterName] = true
			}
		}

		// Convert set to sorted slice
		for clusterName := range clusterNameSet {
			data.ClusterNames = append(data.ClusterNames, clusterName)
		}
		// Sort cluster names for consistent UI
		sort.Strings(data.ClusterNames)
	}

	if search {
		versions, err := s.webServiceInjector.GetVersions()
		if err != nil {
			s.logger.Error("cannot get versions for dashboard", "msg", err.Error())
			// this is bad, but the dashboard can still show instances without the lastest versions
		}
		data.Versions = versions

		instances, err := s.webServiceInjector.GetInstances(data.SearchOptions)
		if err != nil {
			s.logger.Error("cannot get instances for dashboard", "msg", err.Error())
			http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
			return
		}
		data.Instances = instances
	}

	templatePath := "./internal/services/presentation/webui/templates/index.html"
	b, err := os.ReadFile(templatePath)
	if err != nil {
		s.logger.Error("cannot open template", "error", err.Error(), "filepath", templatePath)
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	tmpl, err := template.New("index").Funcs(sprig.FuncMap()).Parse(string(b))
	if err != nil {
		s.logger.Error("cannot parse template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
		return
	}

	err = tmpl.ExecuteTemplate(w, "index", data)
	if err != nil {
		s.logger.Error("cannot execute template", "error", err.Error())
		http.Error(w, "an internal server error occurred", http.StatusInternalServerError)
	}
}
