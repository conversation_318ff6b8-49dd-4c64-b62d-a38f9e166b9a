package inmemory

import (
	"testing"

	"github.com/stretchr/testify/require"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func TestFilterTexts(t *testing.T) {

	require := require.New(t)

	repo := core.RepositoryFromURL("https://github.company.com/org/repo")

	tests := []struct {
		name        string
		searchTerms []string
		instance    core.Instance
		expected    bool
	}{
		{
			name:        "qualifier only, like",
			searchTerms: []string{"matchme"},
			instance: core.Instance{
				Qualifiers: map[string]string{
					"id": "matchmebutnotexact",
				},
				Version: core.VersionFromString(repo, "v1.0.0"),
			},
			expected: true,
		},
		{
			name:        "qualifier only, like. negative",
			searchTerms: []string{"dontmatchme"},
			instance: core.Instance{
				Qualifiers: map[string]string{
					"id": "matchmebutnotexact",
				},
				Version: core.VersionFromString(repo, "v1.0.0"),
			},
			expected: false,
		},
		{
			name:        "qualifier only, exact",
			searchTerms: []string{`"matchmebutexact"`},
			instance: core.Instance{
				Qualifiers: map[string]string{
					"id": "matchmebutexact",
				},
				Version: core.VersionFromString(repo, "v1.0.0"),
			},
			expected: true,
		},
		{
			name:        "qualifier only, exact, negative",
			searchTerms: []string{`"dontmatchmebutexact"`},
			instance: core.Instance{
				Qualifiers: map[string]string{
					"id": "matchmebutexact",
				},
				Version: core.VersionFromString(repo, "v1.0.0"),
			},
			expected: false,
		},
		{
			name:        "version, like",
			searchTerms: []string{"v1"},
			instance: core.Instance{
				Qualifiers: map[string]string{
					"id": "something",
				},
				Version: core.VersionFromString(repo, "v1.0.0"),
			},
			expected: true,
		},
		{
			name:        "version, like, negative",
			searchTerms: []string{"v0"},
			instance: core.Instance{
				Qualifiers: map[string]string{
					"id": "something",
				},
				Version: core.VersionFromString(repo, "v1.0.0"),
			},
			expected: false,
		},
		{
			name:        "version, exact",
			searchTerms: []string{`"v1.0.0"`},
			instance: core.Instance{
				Qualifiers: map[string]string{
					"id": "something",
				},
				Version: core.VersionFromString(repo, "v1.0.0"),
			},
			expected: true,
		},
		{
			name:        "version, exact, negative",
			searchTerms: []string{`"v1.2.3"`},
			instance: core.Instance{
				Qualifiers: map[string]string{
					"id": "something",
				},
				Version: core.VersionFromString(repo, "v1.0.0"),
			},
			expected: false,
		},
	}

	for testIdx, test := range tests {
		filterOp := filterText(test.searchTerms)
		require.Equalf(test.expected, filterOp(test.instance), "[%d] %s: did not match expected filter result", testIdx, test.name)
	}
}
