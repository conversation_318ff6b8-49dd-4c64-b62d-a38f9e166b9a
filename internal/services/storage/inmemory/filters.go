package inmemory

import (
	"strings"

	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

type filterInstance func(core.Instance) bool

func filterKind(kinds []string) filterInstance {
	return func(instance core.Instance) bool {
		if len(kinds) == 0 {
			return true
		}
		instanceKind, found := instance.Qualifiers["kind"]
		if !found {
			// case: instance no kind, but has a filter
			return false
		}
		for _, kind := range kinds {
			if kind == instanceKind {
				return true
			}
		}
		return false
	}
}

func filterText(texts []string) filterInstance {
	return func(instance core.Instance) bool {
		if len(texts) == 0 {
			return true
		}

		fits := 0
		for _, text := range texts {
			trimmedText := strings.TrimSpace(text)
			if len(trimmedText) == 0 {
				fits += 1
				continue
			}

			var compareOp func(string, string) bool
			if strings.HasPrefix(trimmedText, `"`) && strings.HasSuffix(trimmedText, `"`) {
				compareOp = func(s1, s2 string) bool {
					return strings.Trim(s1, `"`) == strings.Trim(s2, `"`)
				}
			} else {
				compareOp = strings.Contains
			}

			continueParent := false
			for _, qualifierValue := range instance.Qualifiers {
				if compareOp(qualifierValue, trimmedText) {
					fits += 1
					continueParent = true
					break
				}
			}

			if continueParent {
				continue
			}

			if compareOp(instance.Version.FullVersion, trimmedText) {
				fits += 1
				continueParent = true
			}
		}

		return fits == len(texts)
	}
}

func filterClusterName(clusterNames []string) filterInstance {
	return func(instance core.Instance) bool {
		if len(clusterNames) == 0 {
			return true
		}

		// Get cluster name from qualifiers - check multiple possible qualifier keys
		instanceClusterName := ""
		if clusterName, found := instance.Qualifiers["clusterName"]; found {
			instanceClusterName = clusterName
		} else if environment, found := instance.Qualifiers["environment"]; found {
			instanceClusterName = environment
		} else if region, found := instance.Qualifiers["region"]; found {
			instanceClusterName = region
		}

		if instanceClusterName == "" {
			// If no cluster name found, don't match any filter
			return false
		}

		for _, clusterName := range clusterNames {
			if clusterName == instanceClusterName {
				return true
			}
		}
		return false
	}
}
