package inmemory

import (
	"bytes"
	"sort"

	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func serializeInstanceIdentity(instance core.Instance) string {

	// care: looping the map directly is non-deterministic because no order

	var keys []string
	for key := range instance.Qualifiers {
		keys = append(keys, key)
	}

	sort.Strings(keys)

	var buf bytes.Buffer
	for _, key := range keys {
		buf.WriteString(key)
		buf.WriteString("===")
		buf.WriteString(instance.Qualifiers[key])
		buf.WriteString("|||")
	}
	return buf.String()
}

func serializeVersionIdentity(version core.Version) string {
	var buf bytes.Buffer

	buf.WriteString(version.Repository.URL)
	buf.WriteString("|||")
	buf.WriteString(version.Prefix)

	s := buf.String()
	if s == "|||" {
		return ""
	}

	return s
}
