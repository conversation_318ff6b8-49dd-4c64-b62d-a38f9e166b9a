package inmemory

import (
	"errors"
	"sync"
	"time"

	"log/slog"

	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

type service struct {
	logger       *slog.Logger
	deferFuncs   []func()
	instances    map[string]core.Instance
	versions     map[string]core.Version
	instanceLock sync.RWMutex
	versionLock  sync.RWMutex
}

func NewService() (core.StorageService, error) {
	s := &service{
		instances: make(map[string]core.Instance),
		versions:  make(map[string]core.Version),
	}

	return s, nil
}

func (s *service) ID() string {
	return "in-memory"
}

func (s *service) InjectLogger(logger *slog.Logger) {
	s.logger = logger
}

func (s *service) Start() error {
	stopChan := make(chan struct{}, 1)
	go func() {
		ticker := time.NewTicker(15 * time.Second)
		for {
			select {
			case <-stopChan:
				ticker.Stop()
				return
			case <-ticker.C:
				s.logger.Debug("inmemory statistics", "instances", len(s.instances), "versions", len(s.versions))
			}
		}
	}()

	s.deferFuncs = append(s.deferFuncs, func() {
		close(stopChan)
	})
	return nil
}

func (s *service) Stop() error {
	for _, f := range s.deferFuncs {
		f()
	}
	return nil
}

func (s *service) StoreInstance(instance core.Instance) error {
	id := serializeInstanceIdentity(instance)
	if id == "" {
		return errors.New("cannot store instance with qualifiers serializing as empty string")
	}

	s.instanceLock.Lock()
	defer s.instanceLock.Unlock()

	s.instances[id] = instance

	return nil
}

func (s *service) StoreVersion(version core.Version) error {
	id := serializeVersionIdentity(version)
	if id == "" {
		return errors.New("cannot store version with identity serializing as empty string")
	}

	s.versionLock.Lock()
	defer s.versionLock.Unlock()

	s.versions[id] = version

	return nil
}

func (s *service) GetInstances(searchOpts core.InstanceSearchOptions) ([]core.Instance, error) {
	s.instanceLock.RLock()
	defer s.instanceLock.RUnlock()

	instances := []core.Instance{}

	filters := []filterInstance{
		filterKind(searchOpts.Kind),
		filterText(searchOpts.QualifierText),
	}

	for _, instance := range s.instances {
		add := true
		for _, f := range filters {
			add = add && f(instance) // && skips following filters after the first 'false'
		}

		if add {
			instances = append(instances, instance)
		}
	}

	return instances, nil
}

func (s *service) GetVersions() ([]core.Version, error) {
	s.versionLock.RLock()
	defer s.versionLock.RUnlock()

	versions := []core.Version{}

	for _, version := range s.versions {
		versions = append(versions, version)
	}

	return versions, nil
}

func (s *service) DeleteInstance(instance core.Instance) error {
	s.instanceLock.Lock()
	defer s.instanceLock.Unlock()

	id := serializeInstanceIdentity(instance)
	if _, exists := s.instances[id]; !exists {
		return core.ErrDoesNotExist
	}

	delete(s.instances, id)
	return nil
}

func (s *service) DeleteVersion(version core.Version) error {
	s.versionLock.Lock()
	defer s.versionLock.Unlock()

	id := serializeVersionIdentity(version)
	if _, exists := s.versions[id]; !exists {
		return core.ErrDoesNotExist
	}

	delete(s.versions, id)
	return nil
}
