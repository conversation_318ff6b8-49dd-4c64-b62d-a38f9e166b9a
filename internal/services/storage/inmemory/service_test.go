package inmemory

import (
	"testing"

	"github.com/stretchr/testify/require"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

var _ core.StorageService = &service{} // ensure interface compatibility

func TestDeleteInstance(t *testing.T) {
	tests := []struct {
		name              string
		initialInstances  []core.Instance
		instanceToDelete  core.Instance
		expectedErr       error
		expectedInstances []core.Instance
	}{
		{
			name: "delete successful",
			initialInstances: []core.Instance{
				{
					Qualifiers: map[string]string{
						"id": "a",
					},
				},
				{
					Qualifiers: map[string]string{
						"id": "b",
					},
				},
				{
					Qualifiers: map[string]string{
						"id": "c",
					},
				},
			},
			instanceToDelete: core.Instance{
				Qualifiers: map[string]string{
					"id": "b",
				},
			},
			expectedErr: nil,
			expectedInstances: []core.Instance{
				{
					Qualifiers: map[string]string{
						"id": "a",
					},
				},
				{
					Qualifiers: map[string]string{
						"id": "c",
					},
				},
			},
		},
		{
			name: "delete non-existing",
			initialInstances: []core.Instance{
				{
					Qualifiers: map[string]string{
						"id": "a",
					},
				},
			},
			instanceToDelete: core.Instance{
				Qualifiers: map[string]string{
					"id": "b",
				},
			},
			expectedErr: core.ErrDoesNotExist,
			expectedInstances: []core.Instance{
				{
					Qualifiers: map[string]string{
						"id": "a",
					},
				},
			},
		},
		{
			name:             "delete from empty",
			initialInstances: []core.Instance{},
			instanceToDelete: core.Instance{
				Qualifiers: map[string]string{
					"id": "b",
				},
			},
			expectedErr:       core.ErrDoesNotExist,
			expectedInstances: []core.Instance{},
		},
	}

	require := require.New(t)

	for idx, test := range tests {
		service := &service{
			instances: make(map[string]core.Instance),
		}
		for _, initialInstance := range test.initialInstances {
			id := serializeInstanceIdentity(initialInstance)
			service.instances[id] = initialInstance
		}

		require.Equalf(
			len(test.initialInstances),
			len(service.instances),
			"[%d] %s - sanity check init successful", idx, test.name)

		err := service.DeleteInstance(test.instanceToDelete)
		require.Equalf(
			test.expectedErr,
			err,
			"[%d] %s - error when deleting", idx, test.name)

		require.Equalf(
			len(test.expectedInstances),
			len(service.instances),
			"[%d] %s - length of remaining instance in service", idx, test.name)

		for instanceIdx, expectedInstance := range test.expectedInstances {
			id := serializeInstanceIdentity(expectedInstance)
			serviceInstance, exists := service.instances[id]
			require.Truef(exists, "[%d] %s / %s - remaining instance exists", idx, test.name, instanceIdx)
			require.Truef(
				expectedInstance.EqualIdentity(&serviceInstance),
				"[%d] %s / %s - remaining instance identity", idx, test.name, instanceIdx)
		}
	}
}

func TestDeleteVersion(t *testing.T) {
	tests := []struct {
		name             string
		initialVersions  []core.Version
		versionToDelete  core.Version
		expectedErr      error
		expectedVersions []core.Version
	}{
		{
			name: "delete successful",
			initialVersions: []core.Version{
				core.VersionFromString(
					core.RepositoryFromURL("https://git.example.com/org/repo"),
					"cet-cockpit/v0.1.0",
				),
				core.VersionFromString(
					core.RepositoryFromURL("https://git.example.com/org/repo"),
					"cloud-framework-cockpit/v0.1.0",
				),
			},
			versionToDelete: core.VersionFromString(
				core.RepositoryFromURL("https://git.example.com/org/repo"),
				"cloud-framework-cockpit/v0.1.0",
			),
			expectedErr: nil,
			expectedVersions: []core.Version{
				core.VersionFromString(
					core.RepositoryFromURL("https://git.example.com/org/repo"),
					"cet-cockpit/v0.1.0",
				),
			},
		},
		{
			name: "delete non-existing",
			initialVersions: []core.Version{
				core.VersionFromString(
					core.RepositoryFromURL("https://git.example.com/org/repo"),
					"cet-cockpit/v0.1.0",
				),
			},
			versionToDelete: core.VersionFromString(
				core.RepositoryFromURL("https://git.example.com/org/repo"),
				"cloud-framework-cockpit/v0.1.0",
			),
			expectedErr: core.ErrDoesNotExist,
			expectedVersions: []core.Version{
				core.VersionFromString(
					core.RepositoryFromURL("https://git.example.com/org/repo"),
					"cet-cockpit/v0.1.0",
				),
			},
		},
		{
			name:            "delete from empty",
			initialVersions: []core.Version{},
			versionToDelete: core.VersionFromString(
				core.RepositoryFromURL("https://git.example.com/org/repo"),
				"cet-cockpit/v0.1.0",
			),
			expectedErr:      core.ErrDoesNotExist,
			expectedVersions: []core.Version{},
		},
	}

	require := require.New(t)

	for idx, test := range tests {
		service := &service{
			versions: make(map[string]core.Version),
		}
		for _, initialVersion := range test.initialVersions {
			id := serializeVersionIdentity(initialVersion)
			service.versions[id] = initialVersion
		}

		require.Equalf(
			len(test.initialVersions),
			len(service.versions),
			"[%d] %s - sanity check init successful", idx, test.name)

		err := service.DeleteVersion(test.versionToDelete)
		require.Equalf(
			test.expectedErr,
			err,
			"[%d] %s - error when deleting", idx, test.name)

		require.Equalf(
			len(test.expectedVersions),
			len(service.versions),
			"[%d] %s - length of remaining instance in service", idx, test.name)

		for versionIdx, expectedVersion := range test.expectedVersions {
			id := serializeVersionIdentity(expectedVersion)
			serviceVersion, exists := service.versions[id]
			require.Truef(exists, "[%d] %s / %s - remaining version exists", idx, test.name, versionIdx)
			require.Truef(
				expectedVersion.EqualIdentity(&serviceVersion),
				"[%d] %s / %s - remaining version identity", idx, test.name, versionIdx)
		}
	}
}
