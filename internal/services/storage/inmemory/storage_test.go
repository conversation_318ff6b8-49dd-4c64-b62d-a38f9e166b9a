package inmemory

import (
	"testing"

	"github.com/stretchr/testify/require"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func TestSerializeInstanceIdentity(t *testing.T) {

	tests := []struct {
		name     string
		instance core.Instance
		expected string
	}{
		{
			name: "argo + test other infos are not serialized",
			instance: core.Instance{
				Qualifiers: map[string]string{
					"environment": "dev-demo",
					"name":        "cet-cockpit",
					"kind":        "kubernetes",
				},
				Parameters: map[string]string{
					"some": "thing",
				},
				Version: core.Version{
					FullVersion: "cet-cockpit/v0.1.0",
					Comparable:  "v0.1.0",
					Prefix:      "cet-cockpit",
				},
			},
			expected: "environment===dev-demo|||kind===kubernetes|||name===cet-cockpit|||",
		},
		{
			name: "terragrunt",
			instance: core.Instance{
				Qualifiers: map[string]string{
					"environment": "dev-demo",
					"id":          "foundation/config",
					"region":      "eu-central-1",
					"kind":        "terragrunt",
				},
			},
			expected: "environment===dev-demo|||id===foundation/config|||kind===terragrunt|||region===eu-central-1|||",
		},
		{
			name:     "empty",
			instance: core.Instance{},
			expected: "",
		},
	}

	require := require.New(t)

	for idx, test := range tests {
		result := serializeInstanceIdentity(test.instance)
		require.Equal(test.expected, result, "[%d] %s", idx, test.name)
	}
}

func TestSerializeVersionIdentity(t *testing.T) {

	tests := []struct {
		name     string
		version  core.Version
		expected string
	}{
		{
			name: "prefixed + test other infos are not serialized",
			version: core.Version{
				FullVersion: "cet-cockpit/v0.1.0",
				Comparable:  "v0.1.0",
				Prefix:      "cet-cockpit",
				Repository:  core.RepositoryFromURL("https://git.example.com/org/repo"),
			},
			expected: "https://git.example.com/org/repo|||cet-cockpit",
		},
		{
			name:     "empty",
			expected: "",
		},
	}

	require := require.New(t)

	for idx, test := range tests {
		result := serializeVersionIdentity(test.version)
		require.Equal(test.expected, result, "[%d] %s", idx, test.name)
	}
}
