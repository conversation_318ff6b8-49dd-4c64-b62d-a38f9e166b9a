package terragrunt

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestBuildInstanceFromSourceAndPath(t *testing.T) {

	require := require.New(t)

	// handling repo is not handled here, so not part of test!
	tests := []struct {
		name               string
		source             string
		path               string
		expectedErr        error
		expectedVersion    string
		expectedQualifiers map[string]string
		expectedParamaters map[string]string
	}{
		{
			name:            "from modules",
			source:          "git::https://github.example.com/test-org/tf-modules.git//modules/layer/mod?ref=mod/v1.2.3",
			path:            "test-org/terraform-project-sol-demo/env/dev-demo/demo-region/layer/mod/terragrunt.hcl",
			expectedErr:     nil,
			expectedVersion: "mod/v1.2.3",
			expectedQualifiers: map[string]string{
				"region":      "demo-region",
				"path":        "layer/mod",
				"project":     "demo",
				"short alias": "dev-demo",
			},
			expectedParamaters: map[string]string{
				"terragrunt.hcl": "https://github.example.com/test-org/terraform-project-sol-demo/blob/master/env/dev-demo/demo-region/layer/mod/terragrunt.hcl",
				"module@master":  "https://github.example.com/test-org/tf-modules/tree/master/modules/layer/mod",
			},
		},
		{
			name:            "from component",
			source:          "git::https://github.example.com/test-org/tf-component-a.git?ref=v1.2.3",
			path:            "test-org/terraform-project-sol-demo/env/dev-demo/demo-region/layer/mod/terragrunt.hcl",
			expectedErr:     nil,
			expectedVersion: "v1.2.3",
			expectedQualifiers: map[string]string{
				"region":      "demo-region",
				"path":        "layer/mod",
				"project":     "demo",
				"short alias": "dev-demo",
			},
			expectedParamaters: map[string]string{
				"terragrunt.hcl": "https://github.example.com/test-org/terraform-project-sol-demo/blob/master/env/dev-demo/demo-region/layer/mod/terragrunt.hcl",
				"module@master":  "https://github.example.com/test-org/tf-component-a/tree/master/",
			},
		},
		{
			name:            "feat branch",
			source:          "git::https://github.example.com/test-org/tf-component-a.git?ref=feat/something",
			path:            "test-org/terraform-project-sol-demo/env/dev-demo/demo-region/layer/mod/terragrunt.hcl",
			expectedErr:     nil,
			expectedVersion: "feat/something",
			expectedQualifiers: map[string]string{
				"region":      "demo-region",
				"path":        "layer/mod",
				"project":     "demo",
				"short alias": "dev-demo",
			},
			expectedParamaters: map[string]string{
				"terragrunt.hcl": "https://github.example.com/test-org/terraform-project-sol-demo/blob/master/env/dev-demo/demo-region/layer/mod/terragrunt.hcl",
				"module@master":  "https://github.example.com/test-org/tf-component-a/tree/master/",
			},
		},
		{
			name:        "no git remote",
			source:      "https://github.example.com/test-org/tf-component-a.git?ref=feat/something",
			path:        "test-org/terraform-project-sol-demo/env/dev-demo/demo-region/layer/mod/terragrunt.hcl",
			expectedErr: &ErrSourceIsNotRemote{},
		},
		{
			name:        "local",
			source:      "../../../what/ever",
			path:        "test-org/terraform-project-sol-demo/env/dev-demo/demo-region/layer/mod/terragrunt.hcl",
			expectedErr: &ErrSourceIsLocal{},
		},
		{
			name:        "no ref",
			source:      "git::https://github.example.com/test-org/tf-component-a.git",
			path:        "test-org/terraform-project-sol-demo/env/dev-demo/demo-region/layer/mod/terragrunt.hcl",
			expectedErr: &ErrSourceHasNoReference{},
		},
	}

	s := &service{
		host: "github.example.com",
	}

	for idx, test := range tests {
		instance, err := s.buildInstanceFromSourceAndPath(test.source, test.path)
		if test.expectedErr != nil {
			require.ErrorIs(err, test.expectedErr, "[%d] %s: expected specific error kind", idx, test.name)
			continue
		}
		require.Nilf(err, "[%d] %s: expected no error, but got one", idx, test.name)

		require.Equalf(test.expectedVersion, instance.Version.FullVersion, "[%d] %s: expected same full version", idx, test.name)

		require.Equal(len(test.expectedQualifiers), len(instance.Qualifiers), "[%d] %s: expected same amount of qualifiers", idx, test.name)
		for key, expectedQualifier := range test.expectedQualifiers {
			require.Equal(expectedQualifier, instance.Qualifiers[key], "[%d] %s: expected same qualifier %s", idx, test.name, key)
		}

		require.Equal(len(test.expectedParamaters), len(instance.Parameters), "[%d] %s: expected same amount of parameters", idx, test.name)
		for key, expectedParameter := range test.expectedParamaters {
			require.Equal(expectedParameter, instance.Parameters[key], "[%d] %s: expected same parameter %s", idx, test.name, key)
		}
	}
}
