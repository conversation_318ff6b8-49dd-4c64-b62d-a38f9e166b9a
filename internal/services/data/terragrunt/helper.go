package terragrunt

import (
	"fmt"
	"strings"

	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

func (s *service) buildInstanceFromSourceAndPath(source string, path string) (core.Instance, error) {
	instance := core.Instance{
		Qualifiers: map[string]string{},
		Parameters: map[string]string{},
	}

	if strings.HasPrefix(source, "..") {
		return instance, &ErrSourceIsLocal{}
	}
	if !strings.HasPrefix(source, "git::") {
		return instance, &ErrSourceIsNotRemote{}
	}

	for key, value := range s.staticInstanceQualifiers {
		instance.Qualifiers[key] = value
	}

	for key, value := range s.staticInstanceParameters {
		instance.Parameters[key] = value
	}

	url := strings.TrimPrefix(source, "git::")
	urlSplits := strings.Split(url, "//")                              // 1. https: 2. repo 3. mod path
	repo := core.RepositoryFromURL(strings.Join(urlSplits[0:2], "//")) // incl https://

	pathSplits := strings.Split(path, "/")
	instance.Qualifiers["short alias"] = pathSplits[3]
	instance.Qualifiers["region"] = pathSplits[4]
	instance.Qualifiers["path"] = strings.Join(pathSplits[5:len(pathSplits)-1], "/")
	instance.Qualifiers["project"] = strings.TrimPrefix(pathSplits[1], "terraform-project-sol-") // TODO make dynamic

	versionStartIdx := strings.LastIndex(url, "?ref=")
	if versionStartIdx < 0 {
		return instance, &ErrSourceHasNoReference{}
	}
	versionStartIdx += 5 // = len("?ref=")
	instance.Version = core.VersionFromString(repo, url[versionStartIdx:])

	instance.Parameters["terragrunt.hcl"] = fmt.Sprintf("https://%s/%s/%s/blob/master/env/%s/%s/%s/terragrunt.hcl",
		s.host,
		pathSplits[0],
		pathSplits[1],
		instance.Qualifiers["short alias"],
		instance.Qualifiers["region"],
		instance.Qualifiers["path"],
	)

	if strings.Contains(repo.URL, s.host) {
		// care: module path is not always delimited by //
		modulePathStartIdx := strings.Index(url, repo.URL) + len(repo.URL)
		modulePath := url[modulePathStartIdx:]
		modulePath, _, _ = strings.Cut(modulePath, "?") // drop reference if exists. must be first, can contain /
		if !strings.HasPrefix(modulePath, "/") {        // strip any suffix like .git
			_, modulePath, _ = strings.Cut(modulePath, "/")
		}
		modulePath = strings.TrimLeft(modulePath, "/") // drop all / (unknown amount, most likely 1 or 2)

		instance.Parameters["module@master"] = fmt.Sprintf("%s/tree/master/%s", repo.URL, modulePath)
	}

	return instance, nil
}
