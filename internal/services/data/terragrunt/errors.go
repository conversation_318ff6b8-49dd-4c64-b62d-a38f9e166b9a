package terragrunt

type ErrSourceIsLocal struct{}

func (e *ErrSourceIsLocal) Error() string {
	return "source string is local path"
}

type ErrSourceIsNotRemote struct{}

func (e *ErrSourceIsNotRemote) Error() string {
	return "source string is not remote, expected 'git::' prefix"
}

type ErrSourceHasNoReference struct{}

func (e *ErrSourceHasNoReference) Error() string {
	return "source string has no remote, expected '?ref='"
}
