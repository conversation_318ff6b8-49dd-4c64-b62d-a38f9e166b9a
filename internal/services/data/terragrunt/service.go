package terragrunt

import (
	"context"
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"log/slog"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing/transport/http"
	"github.com/google/go-github/github"
	"github.com/hashicorp/hcl/v2"
	"github.com/hashicorp/hcl/v2/hclsyntax"
	"github.com/zclconf/go-cty/cty"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/helpers/clients/ghes"
)

const keyTimestamp = "_timestamp"

var hclHasSourceRef *regexp.Regexp // smoke test to skip all local paths beforehand

func init() {
	hclHasSourceRef = regexp.MustCompile(`\n\s*source[^\n]*\?ref=`) // care: must not be not commented out!
}

type service struct {
	logger                        *slog.Logger
	deferFuncs                    []func()
	coreFuncs                     core.DataServiceInjector
	gh                            ghes.Client
	host                          string
	discoverRepositoriesArguments []DiscoverRepositoriesArguments
	checkoutDirectoryPath         string
	staticInstanceQualifiers      map[string]string
	staticInstanceParameters      map[string]string
}

type ServiceArguments struct {
	GithubClient             ghes.Client
	RepositoryDiscoveries    []DiscoverRepositoriesArguments
	CheckoutDirectoryPath    string
	StaticInstanceQualifiers map[string]string // these are added to all instances (can be overridden)
	StaticInstanceParameters map[string]string // these are added to all instances (can be overridden)
}

func NewService(args *ServiceArguments) (core.Service, error) {
	s := service{}

	s.gh = args.GithubClient
	s.discoverRepositoriesArguments = args.RepositoryDiscoveries
	s.checkoutDirectoryPath = args.CheckoutDirectoryPath

	if args.StaticInstanceQualifiers != nil {
		s.staticInstanceQualifiers = args.StaticInstanceQualifiers
	} else {
		s.staticInstanceQualifiers = make(map[string]string)
	}

	if args.StaticInstanceParameters != nil {
		s.staticInstanceParameters = args.StaticInstanceParameters
	} else {
		s.staticInstanceParameters = make(map[string]string)
	}

	s.host = s.gh.Client().BaseURL.Host // don't use client everywhere -> testing

	return &s, nil
}

func (s *service) Inject(injector core.DataServiceInjector) {
	s.coreFuncs = injector
}

func (s *service) Start() error {

	stopChan := make(chan struct{}, 1)
	go func() {
		ticker := time.NewTicker(10 * time.Minute)
		for {
			select {
			case <-stopChan:
				ticker.Stop()
				return
			case <-ticker.C:
				s.Iterate()
			}
		}
	}()

	s.deferFuncs = append(s.deferFuncs, func() {
		close(stopChan)
	})

	go s.Iterate()

	return nil
}

func (s *service) Stop() error {
	for _, f := range s.deferFuncs {
		f()
	}
	return nil
}

func (s *service) InjectLogger(logger *slog.Logger) {
	s.logger = logger
}

func (s *service) InstanceHandlers() []core.InstanceHandler {
	return []core.InstanceHandler{}
}

func (s *service) ID() string {
	return "terragrunt"
}

type DiscoverRepositoriesArguments struct {
	Organization string
	Topic        string
}

func (s *service) Iterate() {
	s.logger.Info("start iteration of getting terragrunt versions")

	repositories := []core.Repository{}
	for _, discovery := range s.discoverRepositoriesArguments {
		s.logger.Debug("discovering repositories", "org", discovery.Organization, "topic", discovery.Topic)
		r, err := s.DiscoverRepositories(discovery)
		if err != nil {
			s.logger.Error("error while discovering repositories", "msg", err.Error(), "org", discovery.Organization, "topic", discovery.Topic)
			continue
		}
		repositories = append(repositories, r...)
	}

	instances := []core.Instance{}
	for _, repository := range repositories {
		err := s.UpdateRepository(repository)
		if err != nil {
			s.logger.Error("cannot update repository", "msg", err.Error(), "repo", repository.SlogAttributes())
			continue
		}
		instances = append(instances, s.AnalyzeRepository(repository)...)
	}

	for _, instance := range instances {
		s.coreFuncs.StoreInstance(instance)
	}

	myKind, found := s.staticInstanceQualifiers["kind"]
	if found && myKind != "" {
		s.logger.Info("starting terragrunt instance housekeeping")
		storedInstances, err := s.coreFuncs.GetInstances(core.InstanceSearchOptions{
			Kind: []string{s.staticInstanceQualifiers["kind"]},
		})
		if err != nil {
			s.logger.Error("error while getting all terragrunt instances", "msg", err.Error())
		} else {
			for _, instance := range storedInstances {
				createdRaw, exists := instance.Parameters[keyTimestamp]
				if !exists {
					s.logger.Warn("got terragrunt instance without expiration, unknown source, hence not touching", "instance", instance.SlogAttributes())
					continue
				}
				created, err := time.Parse(time.RFC3339, createdRaw)
				if err != nil {
					s.logger.Error("cannot parse instance timestamp", "instance", instance.SlogAttributes(), "timestamp", createdRaw)
					continue
				}
				if created.Before(time.Now().Add(-9 * time.Minute)) { // TODO make configurable, currently depends on fixed 10min iteration frequency
					s.coreFuncs.DeleteInstance(instance)
				}
			}
		}
	} else {
		s.logger.Info("skipping terragrunt instance housekeeping, no static kind")
	}

	s.logger.Info("finished iteration of getting terragrunt versions")
}

func (s *service) DiscoverRepositories(args DiscoverRepositoriesArguments) ([]core.Repository, error) {
	repositories := []core.Repository{}

	listOptions := github.ListOptions{
		PerPage: 100,
	}

	for {
		repos, resp, err := s.gh.Client().Repositories.ListByOrg(context.Background(), args.Organization, &github.RepositoryListByOrgOptions{
			ListOptions: listOptions,
		})
		if err != nil && !errors.Is(err, git.NoErrAlreadyUpToDate) {
			return repositories, fmt.Errorf("cannot list repos of org %s: %w", args.Organization, err)
		}

		for _, repo := range repos {
			for _, t := range repo.Topics {
				if t == args.Topic {
					repository := core.RepositoryFromURL(*repo.CloneURL)
					repositories = append(repositories, repository)
					s.logger.Debug("discovered repository", "repo", repository.SlogAttributes())
				}
			}
		}

		if resp.NextPage == 0 {
			break
		} else {
			listOptions.Page = resp.NextPage
		}
	}

	return repositories, nil
}

func (s *service) repositoryPath(r core.Repository) string {
	return filepath.Join(s.checkoutDirectoryPath, r.Owner, r.Name)
}

func (s *service) UpdateRepository(repository core.Repository) error {
	path := s.repositoryPath(repository)

	repo, err := git.PlainOpen(path)
	if err != nil {
		s.logger.Debug("cannot open repo locally, trying to clone new", "url", repository.URL)

		ghRepo, _, err := s.gh.Client().Repositories.Get(context.Background(), repository.Owner, repository.Name)
		if err != nil {
			return fmt.Errorf("cannot find repository %s/%s: %w", repository.Owner, repository.Name, err)
		}

		accessToken, err := s.gh.AccessToken()
		if err != nil {
			return fmt.Errorf("failed to get ghes access token: %w", err)
		}

		s.logger.Debug("cloning repository", "cloneURL", *ghRepo.CloneURL, "localPath", path)
		repo, err = git.PlainClone(path, false, &git.CloneOptions{
			URL: *ghRepo.CloneURL,
			Auth: &http.BasicAuth{
				Username: "bot",
				Password: accessToken,
			},
		})
		if err != nil {
			return fmt.Errorf("cannot clone repository %s/%s: %w", repository.Owner, repository.Name, err)
		}
	}
	worktree, err := repo.Worktree()
	if err != nil {
		return fmt.Errorf("cannot open worktree of repo %s/%s: %w", repository.Owner, repository.Name, err)
	}

	accessToken, err := s.gh.AccessToken()
	if err != nil {
		return fmt.Errorf("failed to get ghes access token: %w", err)
	}

	s.logger.Debug("pulling repository changes", "localPath", path)
	err = worktree.Pull(&git.PullOptions{
		Auth: &http.BasicAuth{
			Username: "bot",
			Password: accessToken,
		},
	})
	if err != nil && !errors.Is(err, git.NoErrAlreadyUpToDate) {
		return fmt.Errorf("cannot pull repo %s/%s: %w", repository.Owner, repository.Name, err)
	}

	return nil
}

func (s *service) AnalyzeRepository(repository core.Repository) []core.Instance {
	res := []core.Instance{}

	repoPath := s.repositoryPath(repository)

	terragruntHclPaths := []string{}
	filepath.WalkDir(repoPath, func(path string, d fs.DirEntry, err error) error {
		if d.Name() != "terragrunt.hcl" {
			return nil
		}
		terragruntHclPaths = append(terragruntHclPaths, path)

		return nil
	})

	for _, terragruntHclPath := range terragruntHclPaths {

		tgHclBytes, err := os.ReadFile(terragruntHclPath)
		if err != nil {
			s.logger.Error("cannot read terragrunt.hcl", "path", terragruntHclPath, "msg", err.Error())
			continue
		}

		matchHasSourceRef := hclHasSourceRef.FindSubmatch(tgHclBytes)
		if len(matchHasSourceRef) == 0 {
			s.logger.Debug("skip analyzing terragrunt.hcl because has no source string with reference", "path", terragruntHclPath)
			continue
		}

		hclCtxValues := map[string]cty.Value{}

		f, diags := hclsyntax.ParseConfig(tgHclBytes, terragruntHclPath, hcl.Pos{})
		if diags.HasErrors() {
			s.logger.Error("cannot parse terragrunt.hcl", "path", terragruntHclPath, "msg", diags.Error())
			continue
		}

		var blockTerraform, blockLocals *hclsyntax.Block
		for _, block := range f.Body.(*hclsyntax.Body).Blocks {
			if block.Type == "terraform" {
				blockTerraform = block
			} else if block.Type == "locals" {
				blockLocals = block
			}
		}

		if blockLocals != nil && blockTerraform != nil {
			for _, attribute := range blockLocals.Body.Attributes {

				value, diags := attribute.Expr.Value(&hcl.EvalContext{})
				if diags.HasErrors() {
					s.logger.Debug("cannot read local value in terraform", "path", terragruntHclPath, "msg", diags.Error())
					continue
				}

				hclCtxValues[fmt.Sprintf("local.%s", attribute.Name)] = value
				hclCtxValues[attribute.Name] = value
			}
		}

		if blockTerraform != nil {
			hclCtx := &hcl.EvalContext{
				Variables: map[string]cty.Value{
					"local": cty.ObjectVal(hclCtxValues),
				},
			}

			for _, attribute := range blockTerraform.Body.Attributes {
				if attribute.Name != "source" {
					continue
				}

				value, diags := attribute.Expr.Value(hclCtx)
				if diags.HasErrors() {
					// don't log expected err for strings like "${get_path_to_repo_root()}//live/foundation/account_config"
					if !strings.Contains(diags.Error(), "Function calls not allowed") {
						s.logger.Error("cannot get terraform source from terragrunt.hcl", "path", terragruntHclPath, "msg", diags.Error())
					}
					continue
				}

				relPath, err := filepath.Rel(s.checkoutDirectoryPath, terragruntHclPath)
				if err != nil {
					s.logger.Error("cannot build relative path to terragrunt.hcl", "msg", err.Error(), "path", terragruntHclPath, "baseDir", s.checkoutDirectoryPath)
					continue
				}

				instance, err := s.buildInstanceFromSourceAndPath(value.AsString(), relPath)
				if err != nil {
					if _, isLocal := err.(*ErrSourceIsLocal); isLocal {
						// expected
					} else if _, isNotRemote := err.(*ErrSourceIsNotRemote); isNotRemote {
						// expected
					} else {
						s.logger.Error("cannot parse source to instance", "url", value.AsString(), "path", relPath, "msg", err.Error())
					}
					continue
				}

				now, err := time.Now().MarshalText()
				if err != nil {
					s.logger.Error("cannot marshall timestamp, skip cleaning instance", "instance", instance.SlogAttributes())
					continue
				}
				instance.Parameters[keyTimestamp] = string(now)

				res = append(res, instance)
			}
		}

	}
	return res
}
