package ghtags

import (
	"context"
	"fmt"
	"sync"
	"time"

	"log/slog"

	"github.com/google/go-github/github"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/helpers/clients/ghes"
)

type service struct {
	logger         *slog.Logger
	coreFuncs      core.DataServiceInjector
	gh             ghes.Client
	lastUpdate     sync.Map // values of type time.Time
	updateCooldown time.Duration
	host           string
	maxConcurrency int
	maxPages       int
	repoQueue      chan core.Instance
	wg             sync.WaitGroup
}

type ServiceArguments struct {
	GithubClient   ghes.Client
	UpdateCooldown time.Duration
	MaxConcurrency int // Maximum number of concurrent GitHub API calls
	MaxPages       int // Maximum number of tag pages to fetch per repository
}

func NewService(args *ServiceArguments) (core.Service, error) {
	s := service{
		gh: args.GithubClient,
	}

	if args.UpdateCooldown.Abs() > 0 {
		s.updateCooldown = args.UpdateCooldown
	} else {
		s.updateCooldown = 15 * time.Minute
	}

	// Set concurrency defaults
	if args.MaxConcurrency > 0 {
		s.maxConcurrency = args.MaxConcurrency
	} else {
		s.maxConcurrency = 5 // Default to 5 concurrent requests
	}

	if args.MaxPages > 0 {
		s.maxPages = args.MaxPages
	} else {
		s.maxPages = 3 // Default to first 3 pages (300 tags max)
	}

	s.host = s.gh.Client().BaseURL.Host
	s.repoQueue = make(chan core.Instance, 100) // Buffer for repositories to process

	return &s, nil
}

func (s *service) Inject(injector core.DataServiceInjector) {
	s.coreFuncs = injector
}

func (s *service) InjectLogger(logger *slog.Logger) {
	s.logger = logger
}

func (s *service) Start() error {
	s.logger.Debug("github tags update configured", "cooldownMinutes", s.updateCooldown.Minutes(), "host", s.host, "maxConcurrency", s.maxConcurrency, "maxPages", s.maxPages)

	// Start worker goroutines
	for i := 0; i < s.maxConcurrency; i++ {
		s.wg.Add(1)
		go s.worker()
	}

	return nil
}

func (s *service) Stop() error {
	close(s.repoQueue)
	s.wg.Wait()
	return nil
}

func (s *service) InstanceHandlers() []core.InstanceHandler {
	return []core.InstanceHandler{
		s.HandleInstance,
	}
}

func (s *service) ID() string {
	return "ghtags"
}

func (s *service) HandleInstance(instance core.Instance) error {
	if instance.Version.Repository.Host != s.host {
		return nil
	}

	// Check cooldown
	last, found := s.lastUpdate.Load(instance.Version.Repository.URL)
	if found {
		lastTime := last.(time.Time)
		if lastTime.After(time.Now().Add(-1 * s.updateCooldown.Abs())) {
			s.logger.Debug("refresh tags is still on cooldown", "repo", instance.Version.Repository.SlogAttributes())
			return nil
		}
	}

	// Queue the instance for processing by workers
	select {
	case s.repoQueue <- instance:
		// Successfully queued
	default:
		// Queue is full, process synchronously as fallback
		s.logger.Warn("worker queue full, processing synchronously", "repo", instance.Version.Repository.URL)
		return s.processInstance(instance)
	}

	return nil
}

func (s *service) worker() {
	defer s.wg.Done()

	for instance := range s.repoQueue {
		if err := s.processInstance(instance); err != nil {
			s.logger.Error("failed to process instance", "error", err, "repo", instance.Version.Repository.URL)
		}
	}
}

func (s *service) processInstance(instance core.Instance) error {
	var tags []*github.RepositoryTag
	var result []string
	page := 0

	// Limit the number of pages we fetch
	for page < s.maxPages {
		tagsPage, res, err := s.gh.Client().Repositories.ListTags(
			context.Background(),
			instance.Version.Repository.Owner,
			instance.Version.Repository.Name,
			&github.ListOptions{
				PerPage: 100,
				Page:    page,
			})
		if err != nil {
			return fmt.Errorf("failed to list tags at page %d: %w", page, err)
		}

		s.logger.Info("ghtags list tags", "page", page, "status", res.Status, "repo", instance.Version.Repository.URL)
		if len(tagsPage) == 0 {
			break
		}
		tags = append(tags, tagsPage...)
		page++
	}

	for _, tag := range tags {
		result = append(result, *tag.Name)
	}

	s.logger.Debug("Finished fetching tags from GitHub", "repo", instance.Version.Repository.SlogAttributes(), "count", len(tags), "pages", page)

	for _, v := range tagsToLatestVersions(instance.Version.Repository, result) {
		s.coreFuncs.StoreVersion(v)
	}

	s.lastUpdate.Store(instance.Version.Repository.URL, time.Now())
	return nil
}

func tagsToLatestVersions(repo core.Repository, tags []string) []core.Version {
	highest := make(map[string]core.Version)
	for _, tag := range tags {
		this := core.VersionFromString(repo, tag)
		thisSemver, thisIsSemver := this.Semver()
		if !thisIsSemver || this.PreRelease {
			continue // must not put non-semver or release candidates to map
		}

		latest, exists := highest[this.Prefix]
		if !exists {
			highest[this.Prefix] = this
		} else {
			latestSemver, _ := latest.Semver() // cannot have error, filtered out while adding
			if thisSemver.GT(latestSemver) {
				highest[this.Prefix] = this
			}
		}
	}

	res := []core.Version{}
	for _, v := range highest {
		res = append(res, v)
	}
	return res
}
