package ghtags

import (
	"context"
	"fmt"
	"sync"
	"time"

	"log/slog"

	"github.com/google/go-github/github"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/helpers/clients/ghes"
)

type service struct {
	logger         *slog.Logger
	coreFuncs      core.DataServiceInjector
	gh             ghes.Client
	lastUpdate     sync.Map // values of type time.Time
	updateCooldown time.Duration
	host           string
}

type ServiceArguments struct {
	GithubClient   ghes.Client
	UpdateCooldown time.Duration
}

func NewService(args *ServiceArguments) (core.Service, error) {
	s := service{
		gh: args.GithubClient,
	}

	if args.UpdateCooldown.Abs() > 0 {
		s.updateCooldown = args.UpdateCooldown
	} else {
		s.updateCooldown = 15 * time.Minute
	}

	s.host = s.gh.Client().BaseURL.Host

	return &s, nil
}

func (s *service) Inject(injector core.DataServiceInjector) {
	s.coreFuncs = injector
}

func (s *service) InjectLogger(logger *slog.Logger) {
	s.logger = logger
}

func (s *service) Start() error {
	s.logger.Debug("github tags update configured", "cooldownMinutes", s.updateCooldown.Minutes(), "host", s.host)
	return nil
}

func (s *service) Stop() error {
	return nil
}

func (s *service) InstanceHandlers() []core.InstanceHandler {
	return []core.InstanceHandler{
		s.HandleInstance,
	}
}

func (s *service) ID() string {
	return "ghtags"
}

func (s *service) HandleInstance(instance core.Instance) error {
	if instance.Version.Repository.Host != s.host {
		return nil
	}

	last, found := s.lastUpdate.Load(instance.Version.Repository.URL)
	if found {
		lastTime := last.(time.Time)
		if lastTime.After(time.Now().Add(-1 * s.updateCooldown.Abs())) {
			s.logger.Debug("refresh tags is still on cooldown", "repo", instance.Version.Repository.SlogAttributes())
			return nil
		}
	}

	var tags []*github.RepositoryTag
	var result []string
	page := 0
	for {
		tagsPage, res, err := s.gh.Client().Repositories.ListTags(
			context.Background(),
			instance.Version.Repository.Owner,
			instance.Version.Repository.Name,
			&github.ListOptions{
				PerPage: 100,
				Page:    page,
			})
		if err != nil {
			return fmt.Errorf("failed to list tags at page %d: %w", page, err)
		}

		s.logger.Info("ghtags list tags", "page", page, "status", res.Status, "repo", instance.Version.Repository.URL)
		if len(tagsPage) == 0 {
			break
		}
		tags = append(tags, tagsPage...)
		page++
	}

	for _, tag := range tags {
		result = append(result, *tag.Name)
	}

	s.logger.Debug("Finished fetching tags from GitHub", "repo", instance.Version.Repository.SlogAttributes(), "count", len(tags))

	for _, v := range tagsToLatestVersions(instance.Version.Repository, result) {
		s.coreFuncs.StoreVersion(v)
	}

	s.lastUpdate.Store(instance.Version.Repository.URL, time.Now())
	return nil
}

func tagsToLatestVersions(repo core.Repository, tags []string) []core.Version {
	highest := make(map[string]core.Version)
	for _, tag := range tags {
		this := core.VersionFromString(repo, tag)
		thisSemver, thisIsSemver := this.Semver()
		if !thisIsSemver || this.PreRelease {
			continue // must not put non-semver or release candidates to map
		}

		latest, exists := highest[this.Prefix]
		if !exists {
			highest[this.Prefix] = this
		} else {
			latestSemver, _ := latest.Semver() // cannot have error, filtered out while adding
			if thisSemver.GT(latestSemver) {
				highest[this.Prefix] = this
			}
		}
	}

	res := []core.Version{}
	for _, v := range highest {
		res = append(res, v)
	}
	return res
}
