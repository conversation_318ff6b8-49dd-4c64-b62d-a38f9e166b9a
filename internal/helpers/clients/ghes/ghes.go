package ghes

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"time"

	"log/slog"

	"github.com/bradleyfalzon/ghinstallation/v2"
	"github.com/golang-jwt/jwt/v4"
	"github.com/google/go-github/github"
	"golang.org/x/oauth2"
)

type Client interface {
	Client() *github.Client
	AccessToken() (string, error)
}

type client struct {
	args    GitHubEnterpriseClientArguments
	privKey *rsa.PrivateKey
	client  *github.Client
}

type GitHubEnterpriseClientArguments struct {
	ApiURL             string
	AppID              int64
	AppInstallationID  int64
	PrivateKeyFilePath string
	AccessToken        string
}

func NewEnterpriseClient(args GitHubEnterpriseClientArguments) (Client, error) {
	c := &client{
		args: args,
	}
	var err error

	if args.AccessToken != "" {
		slog.Info("creating github enterprise client with access token", "baseURL", args.ApiURL)
		c.client, err = ghesClientFromAccessToken(args)
		if err != nil {
			return nil, fmt.Errorf("error creating github enterprise client for %s: %w", args.ApiURL, err)
		}

		return c, nil
	}

	slog.Info("creating github enterprise client with app credentials")
	c.client, err = ghesClientFromApp(args)
	if err != nil {
		return nil, fmt.Errorf("error creating github enterprise client for %s: %w", args.ApiURL, err)
	}

	privKeyBytes, err := os.ReadFile(args.PrivateKeyFilePath)
	if err != nil {
		return nil, fmt.Errorf("error read ghes private key from file %s: %w", args.PrivateKeyFilePath, err)
	}

	block, _ := pem.Decode(privKeyBytes)
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block containing the private key: %w", err)
	}

	c.privKey, err = x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse RSA private key: %w", err)
	}

	return c, nil
}

func (c *client) Client() *github.Client {
	return c.client
}

func (c *client) AccessToken() (string, error) {

	if c.args.AccessToken != "" {
		return c.args.AccessToken, nil
	}

	claims := jwt.StandardClaims{
		IssuedAt:  time.Now().Add(-1 * time.Minute).Unix(),
		ExpiresAt: time.Now().Add(time.Minute * 5).Unix(),
		Issuer:    strconv.FormatInt(c.args.AppID, 10),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	ss, err := token.SignedString(c.privKey)
	if err != nil {
		return "", err
	}

	var resBody struct {
		Token       string    `json:"token"`
		ExpiresAt   time.Time `json:"expires_at"`
		Permissions struct {
			Contents     string `json:"contents"`
			Metadata     string `json:"metadata"`
			PullRequests string `json:"pull_requests"`
		} `json:"permissions"`
		RepositorySelection string `json:"repository_selection"`
	}

	client := &http.Client{}
	req, err := http.NewRequest("POST", fmt.Sprintf("%s/app/installations/%d/access_tokens", c.args.ApiURL, c.args.AppInstallationID), nil)
	if err != nil {
		return "", err
	}

	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", ss))
	req.Header.Set("Accept", "application/vnd.github.v3+json")

	res, err := client.Do(req)
	if err != nil {
		return "", err
	}

	b, _ := io.ReadAll(res.Body)

	if res.StatusCode < 200 || res.StatusCode > 300 {
		return "", fmt.Errorf("error response. status: %s, msg: %s", res.Status, string(b))
	}

	if err := json.Unmarshal(b, &resBody); err != nil {
		return "", err
	}

	return resBody.Token, nil
}

func ghesClientFromApp(args GitHubEnterpriseClientArguments) (*github.Client, error) {

	appTransport, err := ghinstallation.NewAppsTransportKeyFromFile(http.DefaultTransport, args.AppID, args.PrivateKeyFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to create http transport for github enterprise client: %w", err)
	}
	appTransport.BaseURL = args.ApiURL

	installationTransport := ghinstallation.NewFromAppsTransport(appTransport, args.AppInstallationID)

	client, err := github.NewEnterpriseClient(args.ApiURL, args.ApiURL, &http.Client{Transport: installationTransport})
	if err != nil {
		return nil, fmt.Errorf("failed to create github enterprise http transport client for %s: %w", args.ApiURL, err)
	}

	return client, nil
}

func ghesClientFromAccessToken(args GitHubEnterpriseClientArguments) (*github.Client, error) {
	tokenSource := oauth2.StaticTokenSource(&oauth2.Token{AccessToken: args.AccessToken})
	tokenClient := oauth2.NewClient(context.Background(), tokenSource)

	client, err := github.NewEnterpriseClient(args.ApiURL, args.ApiURL, tokenClient)
	if err != nil {
		return nil, fmt.Errorf("failed to create github enterprise client for %s: %w", args.ApiURL, err)
	}

	return client, nil

}
