package configreader_test

import (
	"testing"

	"github.com/stretchr/testify/require"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/helpers/configreader"
)

type testInit struct {
	Str     string `default:"content"`
	Num     int    `default:"42"`
	Nested  testInitNested
	Without string
}

type testInitNested struct {
	Value string `default:"iamnested"`
}

func TestInitialize(t *testing.T) {
	require := require.New(t)

	tests := []struct {
		name     string
		input    testInit
		expected testInit
	}{

		{
			name: "full",
			input: testInit{
				Str: "hello",
				Num: 4,
				Nested: testInitNested{
					Value: "hello",
				},
				Without: "value",
			},
			expected: testInit{
				Str: "hello",
				Num: 4,
				Nested: testInitNested{
					Value: "hello",
				},
				Without: "value",
			},
		},

		{
			name:  "empty",
			input: testInit{},
			expected: testInit{
				Str: "content",
				Num: 42,
				Nested: testInitNested{
					Value: "iamnested",
				},
				Without: "",
			},
		},
	}

	for idx, test := range tests {
		obj := test.input
		configreader.Initialize(&obj)
		require.Equalf(test.expected, obj, "[%d] %s", idx, test.name)
	}
}
