package configreader

import (
	"fmt"
	"os"
	"reflect"
	"strconv"
	"strings"

	"log/slog"
)

const envPrefix = "ENV_"

// Initialize checks if arbitrary structs' fields have a proper value
// set them to the field tag `default` if they have no value
//
// Attention, these are not supported:
//   - booleans because golang handles false as not set
//   - pointers because you cannot set a memory address statically
//   - arrays and maps because no need (yet)
func Initialize(obj interface{}) {
	objValue := reflect.ValueOf(obj)
	if objValue.Kind() == reflect.Ptr && !objValue.IsNil() {
		objValue = objValue.Elem()
	} else {
		fmt.Println("Invalid input. Must pass a non-nil pointer to a struct.")
		return
	}
	objType := objValue.Type()

	for i := 0; i < objValue.NumField(); i++ {
		fieldValue := objValue.Field(i)
		fieldType := objType.Field(i)

		if fieldValue.String() == "ENV_TEST" {
			slog.Info("stop")
		}

		if fieldValue.Kind() == reflect.Struct {
			Initialize(fieldValue.Addr().Interface())
		} else if defaultValueTag := fieldType.Tag.Get("default"); defaultValueTag != "" && reflect.DeepEqual(fieldValue.Interface(), reflect.Zero(fieldValue.Type()).Interface()) {
			setDefaultValue(fieldValue, defaultValueTag)
		} else if fieldValue.Kind() == reflect.String && strings.HasPrefix(fieldValue.String(), envPrefix) {
			fieldValue.SetString(os.Getenv(strings.TrimPrefix(fieldValue.String(), envPrefix)))
		}
	}
}

func setDefaultValue(fieldValue reflect.Value, defaultValueTag string) {
	switch fieldValue.Kind() {
	case reflect.Int:
		defaultValue, err := strconv.ParseInt(defaultValueTag, 10, 64)
		if err == nil {
			fieldValue.SetInt(defaultValue)
		}
	case reflect.String:
		fieldValue.SetString(defaultValueTag)
	case reflect.Float64:
		defaultValue, err := strconv.ParseFloat(defaultValueTag, 64)
		if err == nil {
			fieldValue.SetFloat(defaultValue)
		}
	}
}
