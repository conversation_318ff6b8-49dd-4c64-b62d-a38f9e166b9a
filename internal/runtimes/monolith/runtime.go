package monolith

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"log/slog"

	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/core"
)

type runtime struct {
	rootLogger    *slog.Logger
	runtimeLogger *slog.Logger
	args          RuntimeArguments
	deferFuncs    []func()
	// ghesClient           *github.Client
	instanceHandlers     []core.InstanceHandler
	dataServiceInjector  core.DataServiceInjector
	dataServices         []core.DataService
	storageService       core.StorageService
	genericServices      []core.Service
	presentationServices []core.PresentationService
}

type RuntimeArguments struct {
	HttpListenAddress string
	RootLogger        *slog.Logger
}

func NewRuntime(args RuntimeArguments) (core.Runtime, error) {

	runtimeLogger := args.RootLogger.With(slog.String("runtime", "monolith"))
	r := &runtime{
		rootLogger:       args.RootLogger,
		runtimeLogger:    runtimeLogger,
		instanceHandlers: []core.InstanceHandler{},
		args:             args,
	}

	r.dataServiceInjector = &dataServiceInjector{
		runtime: r,
	}

	return r, nil
}

func (r *runtime) RegisterService(service core.Service) error {
	r.runtimeLogger.Info("registering service to runtime", "service", service.ID())
	serviceLogger := r.rootLogger.With(slog.String("service", service.ID()))
	service.InjectLogger(serviceLogger)
	if dataService, ok := service.(core.DataService); ok {
		r.dataServices = append(r.dataServices, dataService)
		r.instanceHandlers = append(r.instanceHandlers, dataService.InstanceHandlers()...)
	} else if storageService, ok := service.(core.StorageService); ok {
		if r.storageService != nil {
			return fmt.Errorf("runtime already has a storage service")
		}
		r.storageService = storageService
	} else if presentationService, ok := service.(core.PresentationService); ok {
		r.presentationServices = append(r.presentationServices, presentationService)
	} else {
		r.genericServices = append(r.genericServices, service)
	}
	return nil
}

func (r *runtime) Start() error {
	// start storage first as the other services might use it
	err := r.storageService.Start()
	if err != nil {
		return fmt.Errorf("cannot start storage service %s: %w", r.storageService.ID(), err)
	}
	r.runtimeLogger.Info("service storage started", "service", r.storageService.ID())

	for _, service := range r.dataServices {
		service.Inject(r.dataServiceInjector)
		err = service.Start()
		if err != nil {
			return fmt.Errorf("cannot start data service %s: %w", service.ID(), err)
		} else {
			r.runtimeLogger.Info("data service started", "service", service.ID())
		}
	}

	for _, service := range r.genericServices {
		err = service.Start()
		if err != nil {
			return fmt.Errorf("cannot start generic service %s: %w", service.ID(), err)
		} else {
			r.runtimeLogger.Info("generic service started", "service", service.ID())
		}
	}

	// most likely, presentation services depend on the others
	presentationServiceInjector := &presentationServiceInjector{
		runtime: r,
	}
	rootMux := http.NewServeMux()
	startHttpServer := false
	for _, service := range r.presentationServices {
		service.Inject(presentationServiceInjector)
		err = service.Start()
		if err != nil {
			return fmt.Errorf("cannot start presentation service %s: %w", service.ID(), err)
		}
		mux, err := service.Mux()
		if err != nil {
			return fmt.Errorf("cannot get mux from presentation service %s: %w", service.ID(), err)
		}
		if mux != nil {
			startHttpServer = true
			httpPathPrefix := service.HttpPathPrefix()
			if !strings.HasSuffix(httpPathPrefix, "/") {
				httpPathPrefix = httpPathPrefix + "/" // must hav tailing / to handle full route by service mux
			}
			rootMux.Handle(httpPathPrefix, mux)
			r.runtimeLogger.Info("registered http mux", "service", service.ID(), "prefix", httpPathPrefix)
		}
	}

	if startHttpServer {
		server := &http.Server{
			Addr:         r.args.HttpListenAddress,
			Handler:      rootMux,
			ReadTimeout:  10 * time.Second,
			WriteTimeout: 10 * time.Second,
		}
		go func() {
			r.runtimeLogger.Info("starting runtime http server", "listenAddress", r.args.HttpListenAddress)
			err = server.ListenAndServe()
			if err != nil {
				if !errors.Is(err, http.ErrServerClosed) {
					r.runtimeLogger.Error("runtime http server exited unexpectedly", "msg", err.Error())
				}
			} else {
				r.runtimeLogger.Info("?!?!? runtime http server stopped gracefully", "listenAddress", r.args.HttpListenAddress)
			}
		}()

		r.deferFuncs = append(r.deferFuncs, func() {
			httpShutdownCtx, httpShutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer httpShutdownCancel()

			r.runtimeLogger.Info("stopping runtime http server", "listenAddress", r.args.HttpListenAddress)
			if err := server.Shutdown(httpShutdownCtx); err != nil {
				r.runtimeLogger.Error("error while shutting down the runtime http server", "msg", err.Error())
			} else {
				r.runtimeLogger.Info("runtime http server stopped gracefully", "listenAddress", r.args.HttpListenAddress)
			}
		})
	}

	return nil
}

func (r *runtime) Stop() error {

	for _, f := range r.deferFuncs {
		f()
	}

	for _, service := range r.presentationServices {
		err := service.Stop()
		if err != nil {
			r.runtimeLogger.Error("error while stopping presentation service", "msg", err.Error(), "service", service.ID())
		} else {
			r.runtimeLogger.Info("presentation service stopped", "service", service.ID())
		}
	}

	for _, service := range r.dataServices {
		err := service.Stop()
		if err != nil {
			r.runtimeLogger.Error("error while stopping data service", "msg", err.Error(), "service", service.ID())
		} else {
			r.runtimeLogger.Info("data service stopped", "service", service.ID())
		}
	}

	for _, service := range r.genericServices {
		err := service.Stop()
		if err != nil {
			r.runtimeLogger.Error("error while stopping generic service", "msg", err.Error(), "service", service.ID())
		} else {
			r.runtimeLogger.Info("generic service stopped", "service", service.ID())
		}
	}

	// stop storage last as the other services might use it
	err := r.storageService.Stop()
	if err != nil {
		r.runtimeLogger.Error("error while stopping storage service %s", "msg", err.Error(), "service", r.storageService.ID())
	}
	r.runtimeLogger.Info("storage service stopped", "service", r.storageService.ID())

	return nil
}

type dataServiceInjector struct {
	runtime *runtime
}

func (i *dataServiceInjector) StoreInstance(instance core.Instance) error {
	err := i.runtime.storageService.StoreInstance(instance)
	if err != nil {
		return err
	}
	for _, h := range i.runtime.instanceHandlers {
		err = h(instance)
		if err != nil {
			i.runtime.runtimeLogger.Error("error while handling instance", "msg", err.Error(), "instance", instance.SlogAttributes(), "repo", instance.Version.Repository.SlogAttributes())
		}
	}
	return nil
}

func (i *dataServiceInjector) StoreVersion(version core.Version) error {
	err := i.runtime.storageService.StoreVersion(version)
	if err != nil {
		return err
	}
	return nil
}

func (i *dataServiceInjector) GetInstances(opts core.InstanceSearchOptions) ([]core.Instance, error) {
	return i.runtime.storageService.GetInstances(opts)
}

func (i *dataServiceInjector) GetVersions() ([]core.Version, error) {
	return i.runtime.storageService.GetVersions()
}

func (i *dataServiceInjector) DeleteInstance(inst core.Instance) error {
	return i.runtime.storageService.DeleteInstance(inst)
}

func (i *dataServiceInjector) DeleteVersion(v core.Version) error {
	return i.runtime.storageService.DeleteVersion(v)
}

type presentationServiceInjector struct {
	runtime *runtime
}

func (i *presentationServiceInjector) GetInstances(opts core.InstanceSearchOptions) ([]core.Instance, error) {
	return i.runtime.storageService.GetInstances(opts)
}

func (i *presentationServiceInjector) GetVersions() ([]core.Version, error) {
	return i.runtime.storageService.GetVersions()
}
