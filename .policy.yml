# Requires policy bot installed - https://github.com/palantir/policy-bot

policy:
  approval:
    - dotfiles-changed
    - or:
      - approval-by-main-maintainers
      - approval-by-silence-maintainers
      - fast-track-teams
      - fast-track-developers
      - changelog-changed-by-bot

  disapproval:
    if:
      title:
        matches:
          - "^WIP"
      has_labels:
        - 'lifecycle/rotten'
        - 'lifecycle/stale'
        - 'on hold'
        - 'pr/do-not-merge/work-in-progress'
        - 'wip'

    options:
      methods:
        disapprove:
          comments:
            - ":-1:"
            - "👎"
          github_review: true
        revoke:
          comments:
            - ":+1:"
            - "👍"
          github_review: true
    requires:
      teams: ["VFDE-SOL/sol-admins", "VFDE-SOL/sol-core-contributors"]

approval_rules:

  - name: fast-track-developers
    description: "fast-tracked developers can merge without approval"
    if:
      only_has_contributors_in:
        users: ["mikel-muennekhoff"]
      has_author_in:
        users: ["mikel-muennekhoff"]
    requires:
      count: 0
    options:
      invalidate_on_push: false
      request_review:
        enabled: false

  # team based approvals
  - name: approval-by-main-maintainers
    description: "contributors need approval from a main-maintainer or silence-maintainer member"
    requires:
      count: 1
      teams: ["VFDE-SOL/sol-core-contributors"]
    options:
      invalidate_on_push: true
      request_review:
        enabled: true
        mode: teams

  - name: approval-by-silence-maintainers
    description: "contributors need approval from a main-maintainer or silence-maintainer member (silence)"
    requires:
      count: 1
      teams: ["VFDE-SOL/sol-admins"]
    options:
      invalidate_on_push: true
      request_review:
        enabled: false

  - name: fast-track-teams
    description: "fast-tracked teams can merge without approval"
    if:
      only_has_contributors_in:
        teams: ["VFDE-SOL/sol-core-contributors", "VFDE-SOL/sol-admins"]
      has_author_in:
        teams: ["VFDE-SOL/sol-core-contributors", "VFDE-SOL/sol-admins"]
    requires:
      count: 0
    options:
      invalidate_on_push: false
      request_review:
        enabled: false

  # file based approvals
  - name: dotfiles-changed
    description: "dotfiles changes require approval from sol-admin team"
    if:
      changed_files:
        paths:
          - "^\\..*"
    requires:
      count: 1
      teams: ["VFDE-SOL/sol-admins"]
    options:
      invalidate_on_push: true
      request_review:
        enabled: true
        mode: teams

  - name: changelog-changed-by-bot
    description: "CHANGELOG.md changes from sol-bot pass through"
    if:
      changed_files:
        paths:
          - "CHANGELOG.md"
      only_has_contributors_in:
        users: ["sol-bot[bot]"]
      has_author_in:
        users: ["sol-bot[bot]"]
    requires:
      count: 0
    options:
      invalidate_on_push: false
      request_review:
        enabled: false
