name: pr-labeler

on: pull_request

jobs:
  size-label:
    runs-on: self-hosted
    steps:

      - name: Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-tibdex-github-app-token@v1
        with:
          app_id: ${{ secrets.APP_SOL_BOT_ID }}
          private_key: ${{ secrets.APP_SOL_BOT_PRIVATE_KEY }}

      - name: size-label
        uses: VFDE-SOL/mirror-action-pascalgn-size-label-action@vfde
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}
        with:
          sizes: >
            {
              "0": "XS",
              "10": "S",
              "30": "M",
              "100": "L",
              "500": "XL",
              "1000": "XXL"
            }
