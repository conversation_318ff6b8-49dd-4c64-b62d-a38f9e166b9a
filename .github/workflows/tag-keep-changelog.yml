name: tag-keep-changelog

on:
  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+"
      - "**/[0-9]+.[0-9]+.[0-9]+"

jobs:
  main:
    runs-on: self-hosted
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Required due to the way Git works, without it this action won't be able to find any or the correct tags

      - name: Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-tibdex-github-app-token@v1
        with:
          app_id: ${{ secrets.APP_SOL_BOT_ID }}
          private_key: ${{ secrets.APP_SOL_BOT_PRIVATE_KEY }}

      - name: 📆 Generate Changelog
        id: gen-changelog
        uses: VFDE-SOL/actions/gen-changelog@v1

      - name: 🤖 Create Pull Request
        if: >-
          steps.gen-changelog.outputs.failed != true
        uses: VFDE-SOL/mirror-action-peter-evans-create-pull-request@v4
        with:
          token: ${{ steps.generate_token.outputs.token }}
          committer: GitHub <<EMAIL>>
          commit-message: "chore(changelog): update changelog for ${{ github.ref_name }}"
          author: ${{ github.actor }} <${{ github.actor }}@users.noreply.github.com>
          title: "Update Changelog for ${{ github.ref_name }}"
          add-paths: |
            CHANGELOG.md
          body: |
            Good news everyone,

            There is a new version ${{ github.ref_name }}!

            Let's update that CHANGELOG.md.

            ---
            🤖 I am a bot, beep beep.
          branch: update-changelog/${{ github.ref_name }}
          base: master
