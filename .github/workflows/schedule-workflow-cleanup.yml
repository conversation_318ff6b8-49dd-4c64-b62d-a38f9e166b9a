name: schedule-workflow-cleanup

on:
  schedule:
    - cron: "5 5 * * 1-5" # “At 05:00 GMT-0 on every day-of-week”

  workflow_dispatch:
    inputs:
      days:
        description: "Number of days."
        required: true
        default: "6"
      minimum_runs:
        description: "The minimum runs to keep for each workflow."
        required: true
        default: "6"
      delete_workflow_pattern:
        description: "The name or filename of the workflow. if not set then it will target all workflows."
        required: false
      delete_workflow_by_state_pattern:
        description: "Remove workflow by state: active, deleted, disabled_fork, disabled_inactivity, disabled_manually"
        required: true
        default: "All"
        type: choice
        options:
          - "All"
          - active
          - deleted
          - disabled_inactivity
          - disabled_manually
      delete_run_by_conclusion_pattern:
        description: "Remove workflow by conclusion: action_required, cancelled, failure, skipped, success"
        required: true
        default: "All"
        type: choice
        options:
          - "All"
          - action_required
          - cancelled
          - failure
          - skipped
          - success
      dry_run:
        description: "Only log actions, do not perform any delete operations."
        required: false

jobs:
  cleanup:
    runs-on: self-hosted
    steps:
      - name: Delete workflow runs
        uses: VFDE-SOL/mirror-action-Mattraks-delete-workflow-runs@v2
        with:
          baseUrl: ${{ github.api_url }}
          token: ${{ github.token }}
          repository: ${{ github.repository }}
          retain_days: ${{ github.event.inputs.days || 30 }}
          keep_minimum_runs: ${{ github.event.inputs.minimum_runs || 6 }}
          delete_workflow_pattern: ${{ github.event.inputs.delete_workflow_pattern }}
          delete_workflow_by_state_pattern: ${{ github.event.inputs.delete_workflow_by_state_pattern || 'ALL' }}
          delete_run_by_conclusion_pattern: ${{ github.event.inputs.delete_run_by_conclusion_pattern || 'ALL' }}
          dry_run: ${{ github.event.inputs.dry_run }}
