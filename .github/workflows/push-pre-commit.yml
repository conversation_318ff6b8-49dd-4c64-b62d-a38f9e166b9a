name: push-pre-commit

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  main:
    name: push-pre-commit
    runs-on: self-hosted
    steps:

    - name: Checkout repo
      uses: actions/checkout@v3

    - name: Check if repo has .pre-commit-config.yaml
      id: has_pre_commit_config_file
      uses: VFDE-SOL/mirror-action-andstor-file-existence-action@v2
      with:
        files: '.pre-commit-config.yaml'

    - name: Check if repo has .tool-versions
      id: has_tool_versions_file
      uses: VFDE-SOL/mirror-action-andstor-file-existence-action@v2
      with:
        files: '.tool-versions'

    - name: Move .tool-versions out of the way
      if: steps.has_tool_versions_file.outputs.files_exists == 'true'
      run: |
        mv .tool-versions .tool-versions.bak

    - name: Install pre-commit prerequisites
      if: steps.has_pre_commit_config_file.outputs.files_exists == 'true'
      id: tools # used by task
      uses: VFDE-SOL/actions/asdf-install@v1
      with:
        tool_versions: |
          gitleaks 8.17.0
          golang 1.21.1
          shellcheck 0.9.0
          shfmt 3.7.0
          yq 4.34.2

    - name: Enable github.vodafone.com
      if: steps.has_pre_commit_config_file.outputs.files_exists == 'true'
      uses: VFDE-SOL/action-enable-github-vodafone@v1

    - name: Run pre-commit
      if: steps.has_pre_commit_config_file.outputs.files_exists == 'true'
      uses: VFDE-SOL/mirror-action-pre-commit-action@v3.0.0
      env:
        SKIP: >-
          no-commit-to-branch
