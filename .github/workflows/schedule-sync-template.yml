name: schedule-sync-template

on:
  #schedule:
  #  - cron:  "30 5 * * 1-5" # “At 05:30 GMT-0 on every day-of-week”
  workflow_dispatch:

jobs:
  main:
    runs-on: self-hosted
    if: github.repository != 'VFDE-SOL/template-default'
    steps:
      - name: Generate Token using gh_app sol-github-org-repos
        id: generate_token
        uses: VFDE-SOL/mirror-action-tibdex-github-app-token@v1
        with:
          app_id: ${{ secrets.APP_ORG_REPOS_RW_ID }}
          private_key: ${{ secrets.APP_ORG_REPOS_RW_PRIVATE_KEY }}

      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          token: ${{ steps.generate_token.outputs.token }}

      - name: actions-template-sync
        uses: VFDE-SOL/mirror-action-AndreasAugustin-actions-template-sync@v0.7.3
        with:
          github_token: ${{ steps.generate_token.outputs.token }}
          hostname: github.vodafone.com
          source_repo_path: VFDE-SOL/template-default
          upstream_branch: master # defaults to main
          pr_title: "chore(sync-tpl-files): upstream merge template repository"
          pr_labels: "chore,kind/template-sync"
          pr_commit_msg: "chore(template): merge template changes"
