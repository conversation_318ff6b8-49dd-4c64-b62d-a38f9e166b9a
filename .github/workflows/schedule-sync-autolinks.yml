name: schedule-sync-autolinks

on:
  schedule:
    - cron:  "5 4 * * 6" # “At 04:05 GMT-0 on Saturday”
  workflow_dispatch:

jobs:
  sync:
    runs-on: self-hosted
    steps:
      - name: Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-tibdex-github-app-token@v1
        with:
          app_id: ${{ secrets.APP_SOL_BOT_ORG_ID }}
          private_key: ${{ secrets.APP_SOL_BOT_ORG_PRIVATE_KEY }}

      - name: 🔗 Sync AutoLinks
        uses: VFDE-SOL/actions/sync-autolinks@v1
        with:
          gh_token: ${{ steps.generate_token.outputs.token }}
