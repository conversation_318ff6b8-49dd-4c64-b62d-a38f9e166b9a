name: schedule-stale

on:
  schedule:
    - cron: '30 1 * * *'
  workflow_dispatch:

jobs:
  stale:
    runs-on: self-hosted
    steps:
      - name: Generate Token
        id: generate_token
        uses: VFDE-SOL/mirror-action-tibdex-github-app-token@v1
        with:
          app_id: ${{ secrets.APP_SOL_BOT_ID }}
          private_key: ${{ secrets.APP_SOL_BOT_PRIVATE_KEY }}

      - name: Stale
        uses: actions/stale@v5
        with:
          repo-token: ${{ steps.generate_token.outputs.token }}
          days-before-stale: 15
          days-before-close: 5
          stale-issue-label: lifecycle/stale
          stale-pr-label: lifecycle/stale
          exempt-issue-labels: bug,wip,on hold
          exempt-pr-labels: bug,wip,on hold,do-not-merge/work-in-progress
          exempt-all-milestones: true
          close-issue-label: lifecycle/rotten
          close-pr-label: lifecycle/rotten
          delete-branch: true
          close-issue-message: |
            I'm going to mark this issue as closed this because it has been stalled for 5 days with no activity.

            ---
            🤖 I am a bot, beep beep.
          close-pr-message: |
            I'm going to mark this PR as closed because it has been stalled for 5 days with no activity.

            ---
            🤖 I am a bot, beep beep.
          stale-issue-message: |
            I'm going to mark this issue as stale because it has been open 15 days with no activity.
            Remove stale label or comment or this will be closed in 5 days.

            ---
            🤖 I am a bot, beep beep.
          stale-pr-message: |
            I'm going to mark this PR as stale becaus because it has been open 15 days with no activity.
            Remove stale label or comment or this will be closed in 5 days.

            ---
            🤖 I am a bot, beep beep.
