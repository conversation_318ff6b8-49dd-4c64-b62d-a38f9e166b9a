<h1 align="center">
  CET Cockpit
</h1>

<p align="center">
  <img src="https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/actions/workflows/push-pre-commit.yml/badge.svg" alt="pre-commit" />
</p>

<p align="center">
A web application serving the current deployment status of CET, cross-account.
</p>
<hr />


## Features

- Read the ArgoCD applications: The application connects to the cluster it is deployed in and watches all ArgoCD application custom resources. It parses the chart and current version.
- Read terragrunt.hcl files: The application auto-discovers repositories in the VF GitHub Enterprise Server (based on org and a topic), reads all terragrunt.hcl files to parse the referenced modules and versions.
- GitHub tags: For all identified instances, the application fetches the corresponding source GitHub repository for tags and identifies the latest. Prefixed tags are supported.
- Web UI: The application offers a web interface for easy access and filtering.

## How to deploy

CET Cockpit can be installed via helm, using the helm chart located in [`assembly/helm/chart`](./assembly/helm/chart). Authenticating to the kubernetes cluster works via in-cluster config and the RBAC provided by the chart. To authenticate to GHES, GitHub App credentials are required. Those have to be provided in a secret. The helm chart optionally offers the use of a [external secret](https://external-secrets.io/latest/introduction/getting-started/) from AWS SSM parameter store.

Take a look at the chart's `values.yaml` for configuration options.

Example deployment:
- [ArgoCD application in mgmt](https://github.vodafone.com/VFDE-SOL/k8s-apps-mgmt-sol-vfde/blob/master/apps/templates/apps/cet-cockpit.yaml) ([permalink](https://github.vodafone.com/VFDE-SOL/k8s-apps-mgmt-sol-vfde/blob/5b15b2d2f37a25252fcdec9cd2653f71cf8c1cc5/apps/templates/apps/cet-cockpit.yaml))
- [IRSA role for external secrets](https://github.vodafone.com/VFDE-SOL/terraform-project-sol-mgmt/tree/master/live/application/cet-cockpit)
- Added credentials to SSM Parameter Store manually

CET cockpit supports [ArgoCD Status Badges](https://argo-cd.readthedocs.io/en/stable/user-guide/status-badge/). Follow the ArgoCD docs on how to activate those.

## Development

### Requirements

- `task`: [Official install guide](https://taskfile.dev/installation/) or asdf plugin `task`
- Run `task tool-versions` to get all required tools.
- If you want to use `asdf` for most of the tools, run
  ```shell
  task tool-versions | grep -v '#' > .tool-versions
  cat .tool-versions | awk '{ print $1 }' | xargs -n1 asdf plugin add
  asdf install
  ```

### Local configuration

You need a local configuration file. For this and other files, you can create a `gitignored` directory in the repository root. Create `gitignored/config.yaml` like this (adjust as needed):

```yaml
services:
    argoApps:
        enabled: true
        clusterContext:
            file: /home/<USER>/.kube/config
            name: kind-cet-cockpit
    terragrunt:
        enabled: true
        discovery:
            - organization: VFDE-SOL
              topic: terraform-project
        checkoutPath: ./gitignored/iac_repos
    gitHubTags:
        enabled: false
    webUI:
        enabled: true

gitHub:
    apiURL: https://github.vodafone.com/api/v3 # must not have tailing /
    AccessToken: ENV_GHES_ACCESS_TOKEN
    # appID: 0
    # appInstallationID: 0
    # privateKeyFilePath: ./gitignored/ghes_privkey.txt

debug: false
```

- `service.terragrunt.checkoutPath` points to where the discovered repositories are checked out to if you run the application locally.
- You can point the cluster context to any cluster you are authenticated to and in which you are authorized to watch ArgoCD apps. You can use an EKS or the local kind cluster (see below).
- You can enable and disable services (e.g. here, `gitHubTags` is disabled so no tags are pulled and no update types are calculated)
- You must authenticate to VF GHES if you don't disable the terragrunt and tags service. There are two options to do so
  - Personal access token: Create your PAT and either put it directly in the file or in an environment variable and reference it via `ENV_<ENV_VAR_NAME>`
  - App credentials: This is the same mechanism as for the in-cluster application. You must provide `appID` and `appInstallationID` directly and `privateKeyFilePath` must point to a file with the corresponding private key. You have to create this file.
- When everything's set up, run `task run` to run the application locally.
- `debug` enables debug logging

### Create a local kind cluster

You can run a local cluster to work on in-cluster features or to have a dedicated environment to simulate custom resource behavior.

To create a cluster, run `task cluster:create`. After it has finished, run `task cluster:bootstrap` to create in-cluster content like installing ArgoCD CRDs and a demo application CR.

To use the cluster with a locally running CET cockpit, make sure to point it to the cluster context named `kind-cet-cockpit`.

You can use `kubectl` to do anything in the cluster without fear of breaking anything. You can always destroy and recreate the cluster with `task cluster:delete` and the before-mentioned steps.

### Create containers

Run `task container:build` to create a container based on the current code. It is tagged as `cet-cockpit:dev`. You can run this container with docker (not in the kind cluster!) with `task container:run`.

### Deploy chart to kind cluster

The cluster does not automatically use your local image registry. You must make the image available to the cluster with `task cluster:load-image`. You have to re-run this every time you build a new container image locally.

You must add GHES authentication to the cluster as a secret. Create `assembly/cluster/contents/ghes-credentials.yaml`, add the missing data and re-run `task cluster:bootstrap`.

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: ghes-credentials
  namespace: cet-cockpit
data:
  githubApiUrl: aHR0cHM6Ly9naXRodWIudm9kYWZvbmUuY29tL2FwaS92Mw==
  githubAppID: MTE...
  githubAppInstallationID: MTU...
  githubAppPrivateKey: LS0t...
```

To deploy the chart, run `task helm:deploy`. You can see the application logs with `task helm:logs` and undeploy with `task helm:undeploy`.

### ECR

Use `task ecr:login` to authenticate with your usual AWS auth setup (it uses `with_sol_mgmt` in the background). Then, you run `task ecr:tag` to tag the current `cet-cockpit:dev` image as `<ECR_REPO>/cet-cockpit:latest`. You can push it with `task ecr:push`. This fails if it existed before, as images are set up as immutable. You can delete the latest ECR image with `task ecr:delete-latest`, but be careful and only run this if you absolutely know what you are doing! Remember that images are referenced in real clusters.

### Vulnerability scan

Run `task scan` to trigger a few SAST tools. You can see the results in your terminal, but they are also written to the `dist` directory.

## Release

Use `task release -- <VERSION>` to create a new release. This creates a new tag, builds the container from the local code, tags it properly and pushes it to ECR. It also pushed the tag to the GitHub repository. Note: As smoke test to prevent mistakes, you can only run this from master branch.
