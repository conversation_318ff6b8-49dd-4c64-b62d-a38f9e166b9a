version: '3'

includes:
  container:
    taskfile: ./assembly/container/Taskfile.yaml
    dir: .
    vars:
      CONTAINER_TAG: cet-cockpit:dev
  cluster:
    taskfile: ./assembly/cluster/Taskfile.yaml
    dir: .
    vars:
      CONTAINER_TAG: cet-cockpit:dev
      CLUSTER_NAME: cet-cockpit
  helm:
    taskfile: ./assembly/helm/Taskfile.yaml
    dir: .
    vars:
      CLUSTER_NAME: cet-cockpit
  ecr:
    taskfile: ./assembly/ecr/Taskfile.yaml
    dir: .
    vars:
      CONTAINER_TAG: cet-cockpit:dev

tasks:
  run:
    desc: Start monolith locally
    cmds:
      - go run ./cmd/cet-cockpit --config ./gitignored/config.yaml
  test:
    desc: Run golang tests
    cmds:
      - go test ./...
  scan:
    desc: Run SAST tools
    cmds:
      - mkdir -p dist
      - trivy fs . --skip-dirs gitignored,dist --skip-files .envrc,.gitleaks.toml,.gitleaks.untouched.toml | tee ./dist/trivy.txt
      - trivy config --k8s-version 1.24.0 --severity "LOW,MEDIUM,HIGH,CRITICAL" ./assembly/helm/chart | tee ./dist/trivy-chart.txt
      - govulncheck ./... | tee ./dist/govulncheck.txt
  tool-versions:
    vars:
      BUF:
        sh: mktemp
    desc: Show required tools and versions (asdf-compatible)
    silent: true
    cmds:
      - defer: rm {{ .BUF }}
      - sed -nE 's/^\s*([a-zA-Z0-9_\-]+\s+[0-9]+(\.[0-9]+){0,2})\s*$/\1/p' .github/workflows/push-pre-commit.yml >> {{ .BUF }}
      - |
          cat <<EOF >> {{ .BUF }}
          helm 3.12.2
          kind 0.20.0
          kubectl 1.24.12
          pre-commit 3.3.0
          task 3.28.0
          trivy 0.43.0

          # go install golang.org/x/vuln/cmd/govulncheck@latest
          EOF
      - cat {{ .BUF }}

  release:
    desc: Release a new version (req. version as CLI arg)
    vars:
      VERSION:
        sh: echo "{{ .CLI_ARGS }}" | sed -E 's/^[^0-9]*(.+)/v\1/'
    cmds: # as ecr images are immutable, this fails for existing releases on ecr:push
      - '[ -n "{{ .VERSION }}" ] || (echo "no release version provided" && exit 1)'
      - '[ "$(git symbolic-ref --short HEAD)" == "master" ] || (echo "releases can only run from master branch" && exit 1)'
      - '[[ "$(yq4 e ".image" ./assembly/helm/chart/values.yaml)" == *":{{ .VERSION }}" ]] || (echo "helm chart does not point to image with release tag ({{ .VERSION }})" && exit 1)'
      - echo Releasing {{ .VERSION }}!
      - task container:build
      - task ecr:login
      - task ecr:tag -- {{ .VERSION }}
      - task ecr:push -- {{ .VERSION }}
      - git tag {{ .VERSION }}
      - git push origin {{ .VERSION }}
