repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    - id: check-added-large-files
    - id: check-case-conflict
    - id: check-executables-have-shebangs
    - id: check-json
    - id: check-merge-conflict
    - id: check-symlinks
    - id: check-yaml
      exclude: '^assembly/helm/chart/templates/.*$'
    - id: detect-aws-credentials
      args:
      - --allow-missing-credentials
    - id: detect-private-key
      exclude: .gitleaks*
    - id: end-of-file-fixer
    - id: fix-byte-order-marker
    - id: forbid-new-submodules
    - id: mixed-line-ending
    - id: pretty-format-json
      args:
      - --autofix
      - --no-sort-keys
      - --indent=2
    - id: trailing-whitespace

  - repo: https://github.com/Lucas-C/pre-commit-hooks
    rev: v1.3.1
    hooks:
      - id: forbid-crlf
      - id: remove-crlf

  - repo: https://github.com/jumanjihouse/pre-commit-hooks
    rev: 3.0.0
    hooks:
      - id: forbid-binary
        exclude_types: ["image", "icon"]
      - id: shellcheck # Needs shellcheck: https://github.com/koalaman/shellcheck
      - id: shfmt # Needs shfmt: https://github.com/mvdan/sh/releases

  - repo: https://github.com/jorisroovers/gitlint
    rev: v0.18.0
    hooks:
    - id: gitlint
      stages: [commit-msg]
      args:
        - --contrib=contrib-title-conventional-commits # https://jorisroovers.com/gitlint/latest/rules/contrib_rules/#ct1-contrib-title-conventional-commits
        - --ignore=body-is-missing,body-min-length
        - --msg-filename # must be last https://jorisroovers.com/gitlint/latest/commit_hooks/#pre-commit

  - repo: https://github.vodafone.com/VFDE-ISS/pre-commit-hooks
    rev: v1.4.0
    hooks:
      - id: gitleaks
        args:
        - --no-banner
