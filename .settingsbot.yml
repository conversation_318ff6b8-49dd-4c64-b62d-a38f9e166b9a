repository:
  description: "VFDE-SOL Repository for CET cockpit"
  homepage: ""
  has_wiki: false
  has_issues: true
  has_pages: false
  has_projects: false
  has_downloads: false
  is_template: false

  # Merge options: at least one must be true
  allow_rebase_merge: true
  allow_squash_merge: true
  allow_merge_commit: false

  allow_auto_merge: false
  delete_branch_on_merge: true

  # Extends logic:
  # Topics are always replaced, never merged with _extends.
  topics:
    - settingsbot-managed

branches:
  - name: master
    protection:
      allow_deletions: false
      allow_force_pushes: false
      required_pull_request_reviews:
        required_approving_review_count: 0
        dismiss_stale_reviews: false
        require_code_owner_reviews: false
        dismissal_restrictions:
          enabled: false
          teams: []
      required_status_checks:
        strict: true
        contexts:
          - "Commit<PERSON>hecker"
          - "PolicyBot: master"
          - "SettingsBot"
          - "push-pre-commit"
      enforce_admins: false
      required_conversation_resolution: true
      required_signed_commits: false
      required_linear_history: true

      restrictions:
        enabled: true
        apps: ["sol-bulldozer-bot"]
        teams: ["sol-core-contributors"]

teams:
  - name: sol-admins
    permission: admin
  - name: sol-core-contributors
    permission: admin
