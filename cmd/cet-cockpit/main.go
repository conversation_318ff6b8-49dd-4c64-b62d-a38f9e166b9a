package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"log/slog"

	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/helpers/clients/ghes"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/runtimes/monolith"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/services/data/argoapps"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/services/data/ghtags"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/services/data/terragrunt"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/services/presentation/webui"
	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/services/storage/inmemory"
)

func main() {
	exit := make(chan os.Signal, 1)
	signal.Notify(exit, os.Interrupt, syscall.SIGTERM)

	var configPath string
	flag.StringVar(&configPath, "config", "./config.yaml", "path to config file")
	flag.Parse()

	config, err := LoadConfig(configPath)
	if err != nil {
		slog.Error("error loading config: %w", err)
		os.Exit(1)
	}

	slog.Info("loaded config", "path", configPath)

	monolithArgs := monolith.RuntimeArguments{
		HttpListenAddress: config.HttpListenAddress,
		RootLogger:        slog.Default(),
	}

	if config.Debug {
		monolithArgs.RootLogger = slog.New(slog.NewTextHandler(os.Stderr, &slog.HandlerOptions{
			Level: slog.LevelDebug,
		}))
		slog.Debug("debug logging enabled")
	}

	monolith, err := monolith.NewRuntime(monolithArgs)
	if err != nil {
		slog.Error("error creating monolith runtime: %w", err)
		os.Exit(1)
	}

	ghesClient, err := ghes.NewEnterpriseClient(ghes.GitHubEnterpriseClientArguments{
		ApiURL:             config.GitHub.ApiURL,
		AppID:              config.GitHub.AppID,
		AppInstallationID:  config.GitHub.AppInstallationID,
		PrivateKeyFilePath: config.GitHub.PrivateKeyFilePath,
		AccessToken:        config.GitHub.AccessToken,
	})
	if err != nil {
		slog.Error("cannot create github enterprise client: %w", err)
		os.Exit(1)
	}

	inmemStorage, err := inmemory.NewService()
	if err != nil {
		slog.Error("error creating in-memory storage service: %w", err)
		os.Exit(1)
	}
	err = monolith.RegisterService(inmemStorage)
	if err != nil {
		slog.Error("error registering in-memory storage service", "msg", err.Error())
		os.Exit(1)
	}

	if config.Services.GitHubTags.Enabled {
		ghtags, err := ghtags.NewService(&ghtags.ServiceArguments{
			GithubClient:   ghesClient,
			MaxConcurrency: 5, // Process 5 repositories concurrently
			MaxPages:       3, // Only fetch first 3 pages (300 tags max per repo)
		})
		if err != nil {
			slog.Error("error creating ghtags service: %w", err)
			os.Exit(1)
		}
		err = monolith.RegisterService(ghtags)
		if err != nil {
			slog.Error("error registering github tags service", "msg", err.Error())
			os.Exit(1)
		}
	}

	if config.Services.ArgoApps.Enabled {
		argo, err := argoapps.NewService(&argoapps.ServiceArguments{
			KubeConfigFilePath: config.Services.ArgoApps.ClusterContext.File,
			KubeConfigName:     config.Services.ArgoApps.ClusterContext.Name,
			StaticInstanceQualifiers: map[string]string{
				"kind": "kubernetes",
			},
		})
		if err != nil {
			slog.Error("error creating argocd app service: %w", err)
			os.Exit(1)
		}
		err = monolith.RegisterService(argo)
		if err != nil {
			slog.Error("error registering argocd app service", "msg", err.Error())
			os.Exit(1)
		}
	}

	if config.Services.Terragrunt.Enabled {
		terraArgs := terragrunt.ServiceArguments{
			GithubClient:          ghesClient,
			CheckoutDirectoryPath: config.Services.Terragrunt.CheckoutPath,
			StaticInstanceQualifiers: map[string]string{
				"kind": "terragrunt",
			},
		}

		for _, discovery := range config.Services.Terragrunt.Discovery {
			terraArgs.RepositoryDiscoveries = append(terraArgs.RepositoryDiscoveries, terragrunt.DiscoverRepositoriesArguments{
				Organization: discovery.Organization,
				Topic:        discovery.Topic,
			})
		}
		terra, err := terragrunt.NewService(&terraArgs)
		if err != nil {
			slog.Error("error creating terragrunt service: %w", err)
			os.Exit(1)
		}
		err = monolith.RegisterService(terra)
		if err != nil {
			slog.Error("error registering terragrunt service", "msg", err.Error())
			os.Exit(1)
		}
	}

	if config.Services.WebUI.Enabled {
		webui, err := webui.NewService()
		if err != nil {
			slog.Error("error creating webui presentation service: %w", err)
			os.Exit(1)
		}
		err = monolith.RegisterService(webui)
		if err != nil {
			slog.Error("error registering webui presentation service", "msg", err.Error())
			os.Exit(1)
		}
	}

	monolith.Start()

	<-exit
	slog.Info("gracefully shutting down")
	err = monolith.Stop()
	if err != nil {
		slog.Error("error stopping runtime: %w", err)
		os.Exit(1)
	}
}
