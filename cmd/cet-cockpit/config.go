package main

import (
	"fmt"
	"os"

	"github.vodafone.com/mikel-muennekhoff/argo-watch/internal/helpers/configreader"
	"sigs.k8s.io/yaml"
)

type CliConfig struct {
	Services struct {
		ArgoApps struct {
			Enabled        bool `json:"enabled"`
			ClusterContext struct {
				File string `json:"file"`
				Name string `json:"name"`
			} `json:"clusterContext"`
		} `json:"argoApps"`
		Terragrunt struct {
			Enabled   bool `json:"enabled"`
			Discovery []struct {
				Organization string `json:"organization"`
				Topic        string `json:"topic"`
			} `json:"discovery"`
			CheckoutPath string `default:"./terragrunt_repos" json:"checkoutPath"`
		} `json:"terragrunt"`
		GitHubTags struct {
			Enabled bool `json:"enabled"`
		} `json:"githubTags"`
		WebUI struct {
			Enabled bool `json:"enabled"`
		} `json:"webUI"`
	} `json:"services"`
	GitHub struct {
		ApiURL             string `default:"https://api.github.com" json:"apiURL"`
		AppID              int64  `json:"appID"`
		AppInstallationID  int64  `json:"appInstallationID"`
		PrivateKeyFilePath string `json:"privateKeyFilePath"`
		AccessToken        string `json:"accessToken"`
	} `json:"github"`
	HttpListenAddress string `default:"0.0.0.0:8080" json:"httpListenAddress"`
	Debug             bool   `json:"debug"`
}

func LoadConfig(path string) (CliConfig, error) {
	cfg := CliConfig{}
	f, err := os.ReadFile(path)
	if err != nil {
		return cfg, fmt.Errorf("cannot open file: %w", err)
	}

	err = yaml.Unmarshal(f, &cfg)
	if err != nil {
		return cfg, fmt.Errorf("cannot unmarshall config file: %w", err)
	}

	configreader.Initialize(&cfg)

	return cfg, nil
}
