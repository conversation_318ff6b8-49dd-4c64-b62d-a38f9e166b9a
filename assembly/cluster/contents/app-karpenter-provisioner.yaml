apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: "karpenter-provisioner-spot"
spec:
  destination:
    namespace: kube-system
    server: "https://10.100.0.1:443"
  project: "target"
  source:
    path: charts/karpenter-provisioner/chart
    repoURL: "https://github.vodafone.com/VFDE-SOL/k8s-modules-sol"
    targetRevision: karpenter-provisioner/v1.0.0
    helm:
      values: |
        provisioner:
          name: sol-spot
          weight: 100
          nodeRequirements:
            capacityType: spot
            memory:
              min: 8
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
