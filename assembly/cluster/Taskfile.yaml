# assumes to be included from repo root dir

version: '3'

tasks:
  create:
    desc: Create a local cluster with kind
    cmds:
      - sed 's/CLUSTER_NAME/{{ .CLUSTER_NAME }}/g' ./assembly/cluster/cluster-config.yaml | kind create cluster -v 1 --config -
  delete:
    desc: Delete the local kind cluster
    cmds:
      - kind delete cluster -v 1 --name {{ .CLUSTER_NAME }}
  load-image:
    desc: Load pre-built container to the cluster
    cmds:
      - kind load docker-image {{ .CONTAINER_TAG }} --name {{ .CLUSTER_NAME }}
  bootstrap:
    # care: don't interfere with other local helm repos, always prefix!
    desc: Initialize local cluster contents
    vars:
      KUBECTL: kubectl --context kind-{{ .CLUSTER_NAME }}
      HELM: helm --kube-context kind-{{ .CLUSTER_NAME }}
    cmds:
      - "{{ .HELM }} --kube-context kind-{{ .CLUSTER_NAME }} repo add cet-cockpit-argocd https://argoproj.github.io/argo-helm"
      - defer: "{{ .HELM }} repo remove cet-cockpit-argocd"
      - |
          {{ .HELM }} template argocd cet-cockpit-argocd/argo-cd \
            --namespace argocd \
            --version 5.33.3 \
            --include-crds \
            --no-hooks \
            | yq4 e '. | select(.kind == "CustomResourceDefinition")' \
            | {{ .KUBECTL }} apply -f- --server-side --wait
      - "{{ .KUBECTL }} apply -f ./assembly/cluster/contents"
