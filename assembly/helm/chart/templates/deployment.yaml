apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "fullname" . }}
  labels:
    app: {{ include "fullname" . }}
spec:
  replicas: {{ .Values.replicas | default 1 | int }}
  selector:
    matchLabels:
      app: {{ include "fullname" . }}
  template:
    metadata:
      labels:
        app: {{ include "fullname" . }}
    spec:
      securityContext: {{ .Values.podSecurityContext | toYaml | nindent 8 }}
      serviceAccountName: {{ include "fullname" . }}
      volumes:
        - name: ghes-credentials
          secret:
            secretName: {{ .Values.githubCredentials.secretName }}
        - name: config
          configMap:
            name: {{ include "fullname" . }}-config
        - name: repo-checkout-location
          emptyDir: {}
      containers:
        - name: {{ include "fullname" . }}
          image: {{ .Values.image }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          ports:
          - name: http
            containerPort: {{ .Values.httpPort }}
            protocol: TCP
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources: {{ .Values.resources | toYaml | nindent 12 }}
          securityContext: {{ .Values.containerSecurityContext | toYaml | nindent 12 }}
          env:
            - name: GHES_APP_ID
              valueFrom:
                secretKeyRef:
                  name: ghes-credentials
                  key: {{ .Values.githubCredentials.keys.appID }}
            - name: GHES_APP_INSTALLATION_ID
              valueFrom:
                secretKeyRef:
                  name: ghes-credentials
                  key: {{ .Values.githubCredentials.keys.appInstallationID }}
            - name: GHES_API_URL
              valueFrom:
                secretKeyRef:
                  name: ghes-credentials
                  key: {{ .Values.githubCredentials.keys.apiURL }}
          volumeMounts:
            - name: ghes-credentials
              readOnly: true
              mountPath: "/app/ghes_privkey.pem"
              subPath: {{ .Values.githubCredentials.keys.privateKey }}
            - name: config
              readOnly: true
              mountPath: /app/config
            - name: repo-checkout-location
              mountPath: {{ with dig "config" "Services" "Terragrunt" "CheckoutPath" "./terragrunt_repos" (.Values | merge dict) -}}
                {{ if hasPrefix "/" . }}{{ . }}{{ else -}}
                /app/{{ . }}{{ end }}
                {{ end }}
