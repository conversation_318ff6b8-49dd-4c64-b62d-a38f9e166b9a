{{- if .Values.githubCredentials.externalSecret.enabled -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "fullname" . }}-eso
  labels:
    app: {{ include "fullname" . }}
  annotations:
    eks.amazonaws.com/role-arn: "{{ .Values.githubCredentials.externalSecret.roleArn }}"
---
apiVersion: external-secrets.io/v1alpha1
kind: SecretStore
metadata:
  name: {{ include "fullname" . }}
spec:
  provider:
    aws:
      service: ParameterStore
      region: eu-central-1
      auth:
        jwt:
          serviceAccountRef:
            name: {{ include "fullname" . }}-eso
---
apiVersion: external-secrets.io/v1alpha1
kind: ExternalSecret
metadata:
  name: {{ include "fullname" . }}
spec:
  refreshInterval: 5m0s
  secretStoreRef:
    name: {{ include "fullname" . }}
  target:
    name: {{ .Values.githubCredentials.secretName }}
    creationPolicy: Owner
  data:
    - secretKey: {{ .Values.githubCredentials.keys.appID }}
      remoteRef:
        key: "{{ .Values.githubCredentials.externalSecret.ssmNames.appID }}"
    - secretKey: {{ .Values.githubCredentials.keys.appInstallationID }}
      remoteRef:
        key: "{{ .Values.githubCredentials.externalSecret.ssmNames.appInstallationID }}"
    - secretKey: {{ .Values.githubCredentials.keys.apiURL }}
      remoteRef:
        key: "{{ .Values.githubCredentials.externalSecret.ssmNames.apiURL }}"
    - secretKey: {{ .Values.githubCredentials.keys.privateKey }}
      remoteRef:
        key: "{{ .Values.githubCredentials.externalSecret.ssmNames.privateKey }}"
{{- end -}}
