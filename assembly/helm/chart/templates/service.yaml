apiVersion: v1
kind: Service
metadata:
  labels:
    app: {{ include "fullname" . }}
  name: {{ include "fullname" . }}
spec:
  type: {{ if gt ( .Values.nodePort | int) 29999 }}NodePort{{ else }}ClusterIP{{ end }}
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: http
      {{ if gt (.Values.nodePort | int) 29999 }}nodePort: {{ .Values.nodePort | int }}{{ end }}
  selector:
    app: {{ include "fullname" . }}
