config:
    services:
      argoApps:
          enabled: true
      terragrunt:
        enabled: true
        discovery: # add discovery items to get terragrunt data
          # - organization: org
          #   topic: topic
      gitHubTags:
        enabled: true
      webUI:
        enabled: true

    gitHub:
      # adjust these
      appID: 0
      appInstallationID: 0

      # keep as is
      privateKeyFilePath: /app/ghes_privkey.pem
      apiURL: ENV_GHES_API_URL

    debug: false

# You must provide authentication to ghes if you use terragrunt or gitHubTags service
# You can provide a secret you own way or use external secrets to pull it from AWS SSM.
githubCredentials:
  secretName: REPLACE_ME # this must be replaced
  keys:
    appID: githubAppID
    appInstallationID: githubAppInstallationID
    apiURL: githubApiUrl
    privateKey: githubAppPrivateKey
  externalSecret:
    enabled: false
    roleArn: REPLACE_ME # role to use with the secret store to access SSM
    ssmNames: # SSM param paths for the values
      appID: REPLACE_ME
      appInstallationID: REPLACE_ME
      apiURL: REPLACE_ME
      privateKey: REPLACE_ME


image: 196433213517.dkr.ecr.eu-central-1.amazonaws.com/sol/cet-cockpit:v0.2.0
imagePullPolicy: IfNotPresent

replicas: 1

httpPort: 8080

resources:
  requests:
    cpu: 200m
    memory: 400Mi
  limits:
    cpu: 250m
    memory: 600Mi

podSecurityContext:
  fsGroup: 10001
  supplementalGroups: [10001]

containerSecurityContext:
  runAsNonRoot: true
  runAsUser: 10001
  runAsGroup: 10001
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
      - ALL
  seccompProfile:
    type: RuntimeDefault

# Add this to create a service of type NodePort instead of ClusterIP
# nodePort: 30000
