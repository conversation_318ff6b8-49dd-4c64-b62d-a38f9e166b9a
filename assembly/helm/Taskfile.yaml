# assumes to be included from repo root dir

version: '3'

vars:
  NAMESPACE: cet-cockpit
  RELEASE_NAME: cet-cockpit

tasks:
  deploy:
    desc: Deploy the chart to the local kind cluster
    vars:
      KUBECTL: kubectl --context kind-{{ .CLUSTER_NAME }}
      HELM: helm --kube-context kind-{{ .CLUSTER_NAME }}
    cmds: # note: there's no reloader installed
      - |
        {{ .HELM }} upgrade --install \
          {{ .RELEASE_NAME }} \
          --namespace {{ .NAMESPACE }} \
          --values ./assembly/helm/values.kind.yaml \
          ./assembly/helm/chart
      - "{{ .KUBECTL }} --namespace {{ .NAMESPACE }} delete replicasets --all"
      - "{{ .KUBECTL }} --namespace {{ .NAMESPACE }} delete pods --all"

  undeploy:
    desc: Undeploy the chart from the local kind cluster
    vars:
      HELM: helm --kube-context kind-{{ .CLUSTER_NAME }}
    cmds:
      - "{{ .HELM }} uninstall {{ .RELEASE_NAME }}"
      # don't delete namespace, has other contents (auth secret!)

  logs:
    desc: See logs of the deployed chart
    vars:
      KUBECTL: kubectl --context kind-{{ .CLUSTER_NAME }}
    cmds:
      - "{{ .KUBECTL }} --namespace {{ .NAMESPACE }} logs deploy/cet-cockpit -f"
