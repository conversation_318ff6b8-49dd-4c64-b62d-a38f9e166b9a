version: 3

vars:
  ACCOUNT_ID: "************"
  REPOSITORY: "sol/cet-cockpit" # https://github.vodafone.com/VFDE-SOL/ecr-sol/blob/master/repos/cet-cockpit
  REGION: eu-central-1

tasks:
  login:
    desc: Login to the ECR registry (req. every 12h)
    cmds:
      - $SHELL -ic "with_sol_mgmt aws ecr get-login-password --region {{ .REGION }} | docker login --username AWS --password-stdin {{ .ACCOUNT_ID }}.dkr.ecr.{{ .REGION }}.amazonaws.com"

  tag:
    desc: Tag the latest development image (opt. version als CLI arg, default latest)
    vars:
      FULL_REPO: "{{ .ACCOUNT_ID }}.dkr.ecr.{{ .REGION }}.amazonaws.com/{{ .REPOSITORY }}"
    cmds:
      - docker tag {{ .CONTAINER_TAG }} {{ .FULL_REPO }}:{{ .CLI_ARGS | default "latest" }}
      - docker images | grep -E "{{ .FULL_REPO }}|{{ regexFind "^([^:]+)" .CONTAINER_TAG }}"

  push:
    desc: Push image to ECR (opt. version als CLI arg, default latest)
    vars:
      FULL_REPO: "{{ .ACCOUNT_ID }}.dkr.ecr.{{ .REGION }}.amazonaws.com/{{ .REPOSITORY }}"
    cmds:
      - docker push {{ .FULL_REPO }}:{{ .CLI_ARGS | default "latest" }}

  delete-latest:
    desc: Delete latest image from ECR # because the repo images are immutable
    cmds:
      - $SHELL -ic "with_sol_mgmt aws ecr batch-delete-image --repository-name "{{ .REPOSITORY }}" --image-ids imageTag=latest"
