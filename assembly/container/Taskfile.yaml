# assumes to be included from repo root dir

version: '3'

tasks:
  build:
    desc: Build the container with docker (append base image, default scratch)
    env:
      DOCKER_BUILDKIT: 1 # enable looking for dockerignore next to dockerfile
    vars:
      GOLANG_VERSION:
        sh: sed -nE 's/^\s*golang\s+([0-9]+(\.[0-9]+){0,2})\s*$/\1/p' .github/workflows/push-pre-commit.yml
    cmds:
      - |
          docker build \
            --tag {{ .CONTAINER_TAG }} \
            --file ./assembly/container/Dockerfile \
            --build-arg "GOLANG_VERSION={{ .GOLANG_VERSION }}" \
            {{ if .CLI_ARGS }}--build-arg "BASE={{ .CLI_ARGS }}"{{ end }} \
            .

  run:
    desc: Run the container locally
    cmds:
      - |
          docker run \
          -it --rm \
          -p "8080:8080" \
          -v ./config.yaml:/config.yaml:ro \
          {{ .CONTAINER_TAG }}
