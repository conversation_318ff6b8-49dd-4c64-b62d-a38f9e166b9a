ARG GOLANG_VERSION
# cannot use scratch image (easily), need to validate the GHES cert with a root CA pool
ARG BASE=gcr.io/distroless/static@sha256:7198a357ff3a8ef750b041324873960cf2153c11cc50abb9d8d5f8bb089f6b4e

FROM golang:${GOLANG_VERSION}-alpine AS builder

ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

RUN adduser --uid 10001 --disabled-password cet-user

WORKDIR /app

COPY . .

RUN mkdir /dist && go build -o /dist/cet-cockpit -ldflags '-s -w' -v ./cmd/cet-cockpit

RUN mkdir -p /dist/internal/services/presentation/webui/{templates,static} && \
    cp -r /app/internal/services/presentation/webui/templates /dist/internal/services/presentation/webui/templates && \
    cp -r /app/internal/services/presentation/webui/static /dist/internal/services/presentation/webui/static && \
    chown -R cet-user /dist

FROM ${BASE}

COPY --from=builder /etc/passwd /etc/passwd
USER cet-user

WORKDIR /app

COPY --from=builder /dist /app

EXPOSE 8080

ENTRYPOINT ["/app/cet-cockpit", "--config", "/app/config/config.yaml"]
