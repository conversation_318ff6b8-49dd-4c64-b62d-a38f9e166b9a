# Changelog


<a name="v0.2.0"></a>
## [v0.2.0] - 2024-07-25

### :sparkles: Features
- [`bda0e05`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/bda0e057599c961e28cc29838edc8f75b6d6df93) - **cet-cockpit:** switch to feat branch for eso auth


<a name="v0.1.1"></a>
## [v0.1.1] - 2023-11-30

### :beetle: Bug Fixes
- [`8e56b38`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/8e56b38be59d8577606b5b95ff8a4928c9a9c483) - strip refs tags prefix from versions
- [`2a83f5a`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/2a83f5a8e318946a5126e5465198744df1da9025) - handle unexpected repo url format
- [`d9c093a`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/d9c093a74c7c6fd4480e91ddcf370a9625f663f3) - **argoapps:** switch argo links to public domains
- [`f5f4e95`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/f5f4e9580698a89fc65e770b1468723e49e2c452) - **webui:** dont categorize update if current is higher than latest


<a name="v0.1.0"></a>
## [v0.1.0] - 2023-10-18

### :sparkles: Features
- [`9c33ccb`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/9c33ccb2c1a74b33100cbfad95940e6dd72c5b3a) - add detecting pre release versions
- [`77ecd9b`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/77ecd9b3cb8d4e2b06f02d86e97b4aa4b6956277) - **argoapps:** add argo deep link and status badge
- [`86e0512`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/86e0512034400ccd1cfe5a14dd77f9f6a5e8b1d3) - **argoapps:** add parent application parameter
- [`bd7915e`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/bd7915ed752c034786ee64b6a35b28bb0ed1fb33) - **chart:** increase image version
- [`eafb99c`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/eafb99cbe718069b807c89585e18ca6746ceb82a) - **ghtags:** disqualify release candidates as latest
- [`d8e2915`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/d8e291507fb3e5b63b4e4d842bf0ac17f682a3e3) - **inmemory:** include version in text search
- [`284315a`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/284315aebe6d825472e21e8cf04306b322dcea3e) - **terragrunt:** rename qualifiers

### :beetle: Bug Fixes
- [`8fa56de`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/8fa56dec2e1ff3eb07646a7a725b77ee344f2b6f) - **argoapps:** remove err ref where nil

### :wrench: Refactoring
- [`22ad2e1`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/22ad2e1834dc540f7db0b0b72fda181f9c5e1ff0) - introduce loggers per component


<a name="v0.1.0-rc.4"></a>
## [v0.1.0-rc.4] - 2023-08-21

### :sparkles: Features
- [`97b8633`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/97b86333ccc66d83f73975415f44ad6eb51a7d7e) - **chart:** increase image version

### :beetle: Bug Fixes
- [`cd41460`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/cd414606e320ee095be5af1da4045ea6f06d53c4) - **chart:** re-add required config values


<a name="v0.1.0-rc.3"></a>
## [v0.1.0-rc.3] - 2023-08-21

### :sparkles: Features
- [`e4cfc9b`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/e4cfc9be2052eab58309820feb2421ec0057e33d) - **chart:** increase image version

### :beetle: Bug Fixes
- [`afad32e`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/afad32e661fdf6351c3a83f2fdac6c76e285d5d6) - **webui:** wrong wording in update type filter


<a name="v0.1.0-rc.2"></a>
## [v0.1.0-rc.2] - 2023-08-17

### :sparkles: Features
- [`707855e`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/707855ee2d9e3162ddc80b81e3046a987e3649a6) - **chart:** point image to new ecr tag
- [`b00992c`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/b00992c016a49459f98305d09f30d7682a54541f) - **chart:** adjust memory requests and limits
- [`027e4f0`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/027e4f029c082b343cc9e256a2241efbba7c0943) - **webui:** add search hints


<a name="v0.1.0-rc.1"></a>
## v0.1.0-rc.1 - 2023-08-16

### :sparkles: Features
- [`4a4a8d9`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/4a4a8d9fdafd67b9ef652942260bb803713d8bfb) - enhance instance handling error attributes
- [`f654341`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/f6543417cbbe77b02b0f77201737f0d03cbd8cb8) - add storage providers to core incl in-mem
- [`5e820e5`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/5e820e5a64aef33f61f1dbf59e8eb1ecfb9cd5fd) - add refactored poc, fetch data only
- [`a30fbd4`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/a30fbd47814f2d99795142adcd06f09b8c823715) - add helm chart and deploy to kind cluster
- [`e0850fe`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/e0850feeabeed17e6bfc8974aeedca9a7a1125d2) - add presentation service support with runtime mux
- [`6dc0895`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/6dc089575fcf49344e266cc0748d16e6cd67f5b4) - add web ui like poc
- [`939a0db`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/939a0db73665208c1123032fd54841b583e1fe5f) - add favicon
- [`32e593a`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/32e593ac53e1b3a7a37f6d75492951fab15699af) - add frontend filter with skeleton backend
- [`425a62a`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/425a62a693b0e22cc110abe186ae30cd62d68298) - downgrade env to generic qualifier
- [`948d2c3`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/948d2c36da060622d9e5c6690a1c478c862b7c7e) - add tf module source link
- [`843c60f`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/843c60fd74488538be28731d6bbf1c7db77c9acd) - display terragrunt.hcl button
- [`1920091`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/19200917356c6bff6a09fe08aa93a1b7d68b37e3) - camelcase config
- [`331ef7c`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/331ef7ce7350158e9b097bac1cddd9d132e38b92) - don't show underscored qualifiers and parameters
- [`e768ddc`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/e768ddc04febe380f4a78073a37fe3e829362eae) - add ingress routing
- [`c26b199`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/c26b1993d10ad243348723639fd01c3e8e41a7cd) - add push image to ecr
- [`a8e905e`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/a8e905e38c566f6879665302598762f0c12f23da) - filter gh tags for specific repo host
- [`c9b2988`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/c9b2988bfce2470be606aabfe384083a10d74066) - add security measures
- [`5d3a61a`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/5d3a61a9d625bfe87c95355d80860db961301510) - enforce read-only root fs
- [`679c706`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/679c70698e099236aa1443fbd5f915cacbc697ac) - add backend filtering
- [`e921475`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/e92147532cb4219d621c7598ffe4c64760288f36) - add storage mutex locking
- [`84d2982`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/84d2982d2cde4e9264dfa798935596a144988ecf) - add configuration via yaml file
- [`9c361f1`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/9c361f10f22baf5c8215f57b33f24f13acfaa9e6) - add gh app authentication
- [`3b690ae`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/3b690ae708ece7a1cbf080302b36c6d741feebe5) - add local kind cluster
- [`090d6cc`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/090d6cc9dfa05596186a57e4232fcedf0f1d699d) - add building docker container
- [`46bc2bc`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/46bc2bc43919b95d55ed4130d081d2475e0243bc) - add deleting terragrunt instances in iteration
- [`1818e94`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/1818e9440f7673a889f7aabbd5c57e1d3e4b3ff0) - add deleting argo apps on delete event
- [`318322e`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/318322ec4043c8d5e2720dc2f8cbff2bcfe2bc84) - allow data services to get and delete
- [`703f33c`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/703f33c13d5afa617fbb38f89180aa031c95c1ad) - add deleting instances and versions
- [`add00bf`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/add00bfd6fe829c1080528fa742b6306c1459652) - **argoapps:** add debug messages for watching events
- [`035fb71`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/035fb714f817b74eb7ddedbcfd08512c21c81d3e) - **argoapps:** add chart @ master parameter
- [`760dae8`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/760dae810744c4bc5ab5f068222d0491cd134e05) - **chart:** add external secret for ghes credentials
- [`50d317a`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/50d317a550026309e4494d9764f264fffadaae6f) - **inmemory:** allow more fine-grained search
- [`3eed303`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/3eed303fb0f03f782eeabbe2c8ce32b4c0e12453) - **webui:** adjust html page title
- [`c100112`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/c10011217ec587554c2aa7bc0584125d7686fad1) - **webui:** add vf branding
- [`3c14051`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/3c140511f53f1830be18a771d60ed96c50846a32) - **webui:** add search permalinks
- [`f3dc985`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/f3dc9856f9b39e76ad957cb886cdda5674db4af3) - **webui:** break or truncate long strings
- [`2a1db86`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/2a1db86a1cfe7b8ca45eeece12828ddbd85c5ebf) - **webui:** add update type filter

### :beetle: Bug Fixes
- [`fe293e3`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/fe293e35573308a555695042085d0d4356fd1abb) - tf module link for ghes only
- [`7675fdb`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/7675fdb4c07bfc6ef029d703c144519812d632c3) - repository url parsing
- [`0a6c430`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/0a6c430f1b0c3ff6f9ddc87c380031c8d953e0cb) - in-container tls validation
- [`c039f84`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/c039f84705052181431d28fdc6a1493f2446636a) - terragrunt housekeeping create compare
- [`ac57952`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/ac5795235704f26d1b27b4a7173a33a94f67886a) - get full inital argo app state
- [`0a34266`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/0a34266ca5322bdf0ebeedf1a40556371e6a5b17) - re-add sprig template functions
- [`0d70805`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/0d7080500772df7acc4ab95d3b7bbf56a1fd4c82) - **argoapps:** rebuild app watching to fix crashing
- [`e602a8e`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/e602a8e557eddb0d58b6d3b069d29bb2c891af8f) - **ghes:** stop hiding errors
- [`b8ce908`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/b8ce90824799b777e4174572f6c1a13dd77db698) - **webui:** pass comma-separated search texts correctly

### :wrench: Refactoring
- [`6b511ed`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/6b511ed50c884a7c77fe8891420f2c4388a03038) - drop cron dependency
- [`49bebd9`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/49bebd9bb458f3750ceecb2213df32853a37e9c1) - drop unused storage wrapper
- [`cdde50e`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/cdde50e24d0a7f90cde447f39f34cb04c7f60b01) - use maps for in-mem storage
- [`25a8168`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/25a8168123b80f5fdbedc3b721e4a68aca6ea403) - drop unnecessary fetching while templating
- [`0dd91b2`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/0dd91b228eed9d5bd95c84b9933b15405ead77f1) - consolidate service handling
- [`a72f6e7`](https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/commit/a72f6e7e0eb3d9f7e896020b0fa32764f4b6d1ba) - **ghtags:** get host dynamically from ghes client


[Unreleased]: https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/compare/v0.2.0...HEAD
[v0.2.0]: https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/compare/v0.1.1...v0.2.0
[v0.1.1]: https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/compare/v0.1.0...v0.1.1
[v0.1.0]: https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/compare/v0.1.0-rc.4...v0.1.0
[v0.1.0-rc.4]: https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/compare/v0.1.0-rc.3...v0.1.0-rc.4
[v0.1.0-rc.3]: https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/compare/v0.1.0-rc.2...v0.1.0-rc.3
[v0.1.0-rc.2]: https://github.vodafone.com/VFDE-SOL/tool-cet-cockpit/compare/v0.1.0-rc.1...v0.1.0-rc.2
